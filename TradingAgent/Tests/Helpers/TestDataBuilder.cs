using TradingAgent.Models;

namespace TradingAgent.Tests.Helpers
{
    public static class TestDataBuilder
    {
        public static MarketData CreateBasicMarketData(string symbol = "AAPL")
        {
            return new MarketData
            {
                Symbol = symbol,
                Price = 150.00,
                PERatio = 25.5,
                Sector = "Technology",
                RSI = 55.0,
                RecentPrices = new List<double> { 145.0, 147.0, 149.0, 150.0, 152.0 },
                RawNews = "Apple reports strong quarterly earnings with revenue growth."
            };
        }

        public static EnhancedMarketData CreateEnhancedMarketData(string symbol = "AAPL")
        {
            return new EnhancedMarketData
            {
                Symbol = symbol,
                Price = 150.00,
                PERatio = 25.5,
                Sector = "Technology",
                RSI = 55.0,
                RecentPrices = new List<double> { 145.0, 147.0, 149.0, 150.0, 152.0 },
                RawNews = "Apple reports strong quarterly earnings with revenue growth.",
                LastUpdated = DateTime.UtcNow,
                IsRealTime = true,
                RelatedNews = CreateNewsItems(symbol),
                EconomicData = CreateEconomicIndicators(),
                Volume = 50000000,
                MarketCap = 2500000000000,
                DividendYield = 0.5,
                EPS = 6.15,
                ROE = 15.2,
                DataSource = "TestProvider",
                DayChange = 2.5,
                DayChangePercent = 1.7,
                CompanyName = $"{symbol} Inc.",
                Industry = "Technology",
                Country = "US"
            };
        }

        public static List<NewsItem> CreateNewsItems(string symbol = "AAPL", int count = 3)
        {
            var news = new List<NewsItem>();
            for (int i = 0; i < count; i++)
            {
                news.Add(new NewsItem
                {
                    Title = $"Breaking: {symbol} News Item {i + 1}",
                    Summary = $"Important news about {symbol} affecting market sentiment.",
                    Url = $"https://example.com/news/{i + 1}",
                    PublishedAt = DateTime.UtcNow.AddHours(-i),
                    Source = "Financial Times",
                    SentimentScore = i % 2 == 0 ? 0.8 : 0.5,
                    Keywords = new List<string> { symbol, "earnings", "technology" }
                });
            }
            return news;
        }

        public static EconomicIndicators CreateEconomicIndicators()
        {
            return new EconomicIndicators
            {
                FedFundsRate = 5.25,
                InflationRate = 3.2,
                UnemploymentRate = 3.8,
                GDP_Growth = 2.1,
                VIX = 18.5,
                DXY = 103.2,
                CrudeOilPrice = 75.5,
                GoldPrice = 1950.0,
                LastUpdated = DateTime.UtcNow
            };
        }

        public static MarketDataRequest CreateMarketDataRequest(string symbol = "AAPL")
        {
            return new MarketDataRequest
            {
                Symbol = symbol,
                IncludeNews = true,
                IncludeEconomicData = true,
                NewsLookbackDays = 7,
                PreferredDataSource = "YahooFinance",
                ForceRefresh = false
            };
        }

        public static DataProviderResponse<T> CreateSuccessResponse<T>(T data, string providerName = "TestProvider")
        {
            return new DataProviderResponse<T>
            {
                Success = true,
                Data = data,
                DataSource = providerName,
                ResponseTime = TimeSpan.FromMilliseconds(250),
                FromCache = false,
                ErrorMessage = string.Empty,
                Timestamp = DateTime.UtcNow
            };
        }

        public static DataProviderResponse<T> CreateErrorResponse<T>(string errorMessage, string providerName = "TestProvider")
        {
            return new DataProviderResponse<T>
            {
                Success = false,
                Data = default(T),
                DataSource = providerName,
                ResponseTime = TimeSpan.FromMilliseconds(100),
                FromCache = false,
                ErrorMessage = errorMessage,
                Timestamp = DateTime.UtcNow
            };
        }
    }
}
