using Moq;
using Microsoft.Extensions.Logging;
using TradingAgent.Services;
using TradingAgent.Models;
using TradingAgent.Core;

namespace TradingAgent.Tests.Helpers
{
    public static class TestMockFactory
    {
        public static Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        public static Mock<IMarketDataProvider> CreateMockMarketDataProvider(string providerName = "MockProvider")
        {
            var mock = new Mock<IMarketDataProvider>();
            mock.Setup(x => x.ProviderName).Returns(providerName);
            mock.Setup(x => x.IsHealthyAsync()).ReturnsAsync(true);
            return mock;
        }

        public static Mock<INewsProvider> CreateMockNewsProvider(string providerName = "MockNewsProvider")
        {
            var mock = new Mock<INewsProvider>();
            mock.Setup(x => x.ProviderName).Returns(providerName);
            mock.Setup(x => x.IsHealthyAsync()).ReturnsAsync(true);
            return mock;
        }

        public static Mock<IEconomicDataProvider> CreateMockEconomicDataProvider(string providerName = "MockEconomicProvider")
        {
            var mock = new Mock<IEconomicDataProvider>();
            mock.Setup(x => x.ProviderName).Returns(providerName);
            mock.Setup(x => x.IsHealthyAsync()).ReturnsAsync(true);
            return mock;
        }

        public static Mock<IDataAggregationService> CreateMockDataAggregationService()
        {
            var mock = new Mock<IDataAggregationService>();
            
            // Setup default successful responses
            mock.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((MarketDataRequest request, CancellationToken ct) => 
                    TestDataBuilder.CreateSuccessResponse(TestDataBuilder.CreateEnhancedMarketData(request.Symbol)));

            mock.Setup(x => x.GetAvailableSymbolsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<string> { "AAPL", "GOOGL", "MSFT", "TSLA" });

            mock.Setup(x => x.GetProviderHealthStatusAsync())
                .ReturnsAsync(new Dictionary<string, bool>
                {
                    ["YahooFinance"] = true,
                    ["AlphaVantage"] = true,
                    ["NewsAPI"] = true,
                    ["FRED"] = true
                });

            return mock;
        }

        public static Mock<IOpenAIClient> CreateMockOpenAIClient()
        {
            var mock = new Mock<IOpenAIClient>();

            mock.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync("BUY - Strong fundamentals and positive outlook. Confidence: High");

            mock.Setup(x => x.GetModelForAgent(It.IsAny<string>()))
                .Returns("gpt-4o");

            return mock;
        }

        public static Mock<IAgentEngine> CreateMockAgentEngine()
        {
            var mock = new Mock<IAgentEngine>();

            mock.Setup(x => x.RunAsync(It.IsAny<MarketData>()))
                .ReturnsAsync(new
                {
                    symbol = "AAPL",
                    bias = "BUY",
                    confidence = 0.85,
                    details = new[]
                    {
                        new { agent = "FundamentalAnalyst", Insight = "BUY - Strong fundamentals", Confidence = 0.8, model = "gpt-4o" },
                        new { agent = "MomentumAnalyst", Insight = "Neutral", Confidence = 0.5, model = (string?)null }
                    },
                    processingTimeMs = 150.5
                });

            return mock;
        }



        public static void SetupMarketDataProviderSuccess(Mock<IMarketDataProvider> mock, string symbol = "AAPL")
        {
            var marketData = TestDataBuilder.CreateEnhancedMarketData(symbol);
            var response = TestDataBuilder.CreateSuccessResponse(marketData, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetMarketDataAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var historicalPrices = new List<double> { 145.0, 147.0, 149.0, 150.0, 152.0 };
            var historicalResponse = TestDataBuilder.CreateSuccessResponse(historicalPrices, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetHistoricalPricesAsync(symbol, It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(historicalResponse);
        }

        public static void SetupMarketDataProviderError(Mock<IMarketDataProvider> mock, string errorMessage = "API Error")
        {
            var response = TestDataBuilder.CreateErrorResponse<EnhancedMarketData>(errorMessage, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetMarketDataAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var historicalResponse = TestDataBuilder.CreateErrorResponse<List<double>>(errorMessage, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetHistoricalPricesAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(historicalResponse);
        }

        public static void SetupNewsProviderSuccess(Mock<INewsProvider> mock, string symbol = "AAPL")
        {
            var newsItems = TestDataBuilder.CreateNewsItems(symbol);
            var response = TestDataBuilder.CreateSuccessResponse(newsItems, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetNewsAsync(symbol, It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            mock.Setup(x => x.GetMarketNewsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);
        }

        public static void SetupNewsProviderError(Mock<INewsProvider> mock, string errorMessage = "News API Error")
        {
            var response = TestDataBuilder.CreateErrorResponse<List<NewsItem>>(errorMessage, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetNewsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            mock.Setup(x => x.GetMarketNewsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);
        }

        public static void SetupEconomicDataProviderSuccess(Mock<IEconomicDataProvider> mock)
        {
            var economicData = TestDataBuilder.CreateEconomicIndicators();
            var response = TestDataBuilder.CreateSuccessResponse(economicData, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetEconomicIndicatorsAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);
        }

        public static void SetupEconomicDataProviderError(Mock<IEconomicDataProvider> mock, string errorMessage = "Economic API Error")
        {
            var response = TestDataBuilder.CreateErrorResponse<EconomicIndicators>(errorMessage, mock.Object.ProviderName);
            
            mock.Setup(x => x.GetEconomicIndicatorsAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);
        }
    }
}
