{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "TradingAgent": "Information"}}, "OpenAI": {"ApiKey": "test-api-key", "BaseUrl": "https://api.openai.com/v1/", "DefaultModel": "gpt-4o", "AgentModels": {"FundamentalAnalyst": "gpt-4o", "SentimentAnalyst": "gpt-3.5-turbo", "NewsAnalyst": "gpt-4o", "RiskAnalyst": "gpt-4"}, "MaxTokens": 1000, "Temperature": 0.7, "RequestTimeoutSeconds": 30}, "DataProviders": {"PrimaryMarketDataProvider": "YahooFinance", "PrimaryNewsProvider": "NewsAPI", "MarketDataCacheMinutes": 5, "NewsCacheMinutes": 15, "EconomicDataCacheMinutes": 60, "EnableFallback": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "AlphaVantage": {"ApiKey": "test-alpha-vantage-key", "BaseUrl": "https://www.alphavantage.co/query", "RequestsPerMinute": 5, "RequestsPerDay": 500}, "Finnhub": {"ApiKey": "test-finnhub-key", "BaseUrl": "https://finnhub.io/api/v1", "RateLimitPerMinute": 60, "RateLimitPerSecond": 30}, "Polygon": {"ApiKey": "test-polygon-key", "S3ApiKey": "test-polygon-s3-key", "BaseUrl": "https://api.polygon.io", "RequestsPerMinute": 5}, "NewsApi": {"ApiKey": "test-news-api-key", "BaseUrl": "https://newsapi.org/v2", "RequestsPerDay": 1000}, "Fred": {"ApiKey": "test-fred-key", "BaseUrl": "https://api.stlouisfed.org/fred", "RequestsPerDay": 120000}, "YahooFinance": {"BaseUrl": "https://query1.finance.yahoo.com", "RequestsPerSecond": 2000, "EnableRapidAPI": false, "RapidAPIKey": "", "RapidAPIHost": "yahoo-finance15.p.rapidapi.com"}}, "AllowedHosts": "*"}