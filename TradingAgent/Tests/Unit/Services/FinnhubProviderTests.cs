using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FluentAssertions;
using System.Net.Http;
using Config;
using TradingAgent.Services;
using TradingAgent.Models;

namespace TradingAgent.Tests.Unit.Services
{
    public class FinnhubProviderTests
    {
        private readonly Mock<ILogger<FinnhubProvider>> _mockLogger;
        private readonly Mock<IOptions<DataProviderSettings>> _mockOptions;
        private readonly DataProviderSettings _settings;
        private readonly HttpClient _httpClient;

        public FinnhubProviderTests()
        {
            _mockLogger = new Mock<ILogger<FinnhubProvider>>();
            _mockOptions = new Mock<IOptions<DataProviderSettings>>();
            _settings = new DataProviderSettings
            {
                Finnhub = new FinnhubSettings
                {
                    ApiKey = "test-api-key",
                    BaseUrl = "https://finnhub.io/api/v1",
                    RateLimitPerMinute = 60,
                    RateLimitPerSecond = 30,
                    TimeoutSeconds = 30
                }
            };
            _mockOptions.Setup(x => x.Value).Returns(_settings);
            _httpClient = new HttpClient();
        }

        [Fact]
        public void Constructor_ShouldInitializeCorrectly()
        {
            // Act
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Assert
            provider.Should().NotBeNull();
            provider.ProviderName.Should().Be("Finnhub");
        }

        [Fact]
        public void ProviderName_ShouldReturnFinnhub()
        {
            // Arrange
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Act
            var providerName = provider.ProviderName;

            // Assert
            providerName.Should().Be("Finnhub");
        }

        [Fact]
        public async Task GetMarketDataAsync_WithEmptyApiKey_ShouldReturnError()
        {
            // Arrange
            _settings.Finnhub.ApiKey = "";
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Act
            var result = await provider.GetMarketDataAsync("AAPL");

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.ErrorMessage.Should().Contain("API key is not configured");
            result.DataSource.Should().Be("Finnhub");
        }

        [Fact]
        public async Task GetHistoricalPricesAsync_WithEmptyApiKey_ShouldReturnError()
        {
            // Arrange
            _settings.Finnhub.ApiKey = "";
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Act
            var result = await provider.GetHistoricalPricesAsync("AAPL", 30);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.ErrorMessage.Should().Contain("API key is not configured");
            result.DataSource.Should().Be("Finnhub");
        }

        [Fact]
        public async Task GetNewsAsync_WithEmptyApiKey_ShouldReturnError()
        {
            // Arrange
            _settings.Finnhub.ApiKey = "";
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Act
            var result = await provider.GetNewsAsync("AAPL", 7);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.ErrorMessage.Should().Contain("API key is not configured");
            result.DataSource.Should().Be("Finnhub");
        }

        [Fact]
        public async Task GetMarketNewsAsync_WithEmptyApiKey_ShouldReturnError()
        {
            // Arrange
            _settings.Finnhub.ApiKey = "";
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Act
            var result = await provider.GetMarketNewsAsync(7);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.ErrorMessage.Should().Contain("API key is not configured");
            result.DataSource.Should().Be("Finnhub");
        }

        [Fact]
        public async Task IsHealthyAsync_WithEmptyApiKey_ShouldReturnFalse()
        {
            // Arrange
            _settings.Finnhub.ApiKey = "";
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Act
            var result = await provider.IsHealthyAsync();

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void Settings_ShouldBeConfiguredCorrectly()
        {
            // Arrange & Act
            var provider = new FinnhubProvider(_httpClient, _mockOptions.Object, _mockLogger.Object);

            // Assert
            _settings.Finnhub.ApiKey.Should().Be("test-api-key");
            _settings.Finnhub.BaseUrl.Should().Be("https://finnhub.io/api/v1");
            _settings.Finnhub.RateLimitPerMinute.Should().Be(60);
            _settings.Finnhub.RateLimitPerSecond.Should().Be(30);
            _settings.Finnhub.TimeoutSeconds.Should().Be(30);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _httpClient?.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
