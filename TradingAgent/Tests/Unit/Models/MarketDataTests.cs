using Xunit;
using TradingAgent.Models;
using TradingAgent.Tests.Helpers;
using FluentAssertions;

namespace TradingAgent.Tests.Unit.Models
{
    public class MarketDataTests
    {
        [Fact]
        public void MarketData_DefaultConstructor_ShouldInitializeProperties()
        {
            // Act
            var marketData = new MarketData();

            // Assert
            marketData.Symbol.Should().Be(string.Empty);
            marketData.Price.Should().Be(0.0);
            marketData.PERatio.Should().Be(0.0);
            marketData.Sector.Should().Be(string.Empty);
            marketData.RSI.Should().Be(0.0);
            marketData.RecentPrices.Should().NotBeNull();
            marketData.RawNews.Should().Be(string.Empty);
        }

        [Fact]
        public void MarketData_PropertyAssignment_ShouldWorkCorrectly()
        {
            // Arrange
            var symbol = "AAPL";
            var price = 150.75;
            var peRatio = 25.5;
            var sector = "Technology";
            var rsi = 65.0;
            var recentPrices = new List<double> { 145.0, 147.0, 149.0, 150.0, 152.0 };
            var rawNews = "Apple reports strong earnings";

            // Act
            var marketData = new MarketData
            {
                Symbol = symbol,
                Price = price,
                PERatio = peRatio,
                Sector = sector,
                RSI = rsi,
                RecentPrices = recentPrices,
                RawNews = rawNews
            };

            // Assert
            marketData.Symbol.Should().Be(symbol);
            marketData.Price.Should().Be(price);
            marketData.PERatio.Should().Be(peRatio);
            marketData.Sector.Should().Be(sector);
            marketData.RSI.Should().Be(rsi);
            marketData.RecentPrices.Should().BeEquivalentTo(recentPrices);
            marketData.RawNews.Should().Be(rawNews);
        }

        [Theory]
        [InlineData("AAPL")]
        [InlineData("GOOGL")]
        [InlineData("MSFT")]
        [InlineData("")]
        [InlineData(null)]
        public void MarketData_Symbol_ShouldAcceptVariousValues(string symbol)
        {
            // Act
            var marketData = new MarketData { Symbol = symbol };

            // Assert
            marketData.Symbol.Should().Be(symbol);
        }

        [Theory]
        [InlineData(0.0)]
        [InlineData(100.50)]
        [InlineData(-5.25)]
        [InlineData(double.MaxValue)]
        [InlineData(double.MinValue)]
        public void MarketData_Price_ShouldAcceptVariousValues(double price)
        {
            // Act
            var marketData = new MarketData { Price = price };

            // Assert
            marketData.Price.Should().Be(price);
        }

        [Theory]
        [InlineData(0.0)]
        [InlineData(100.0)]
        [InlineData(-10.0)]
        public void MarketData_RSI_ShouldAcceptVariousValues(double rsi)
        {
            // Act
            var marketData = new MarketData { RSI = rsi };

            // Assert
            marketData.RSI.Should().Be(rsi);
        }

        [Fact]
        public void MarketData_RecentPrices_ShouldAcceptEmptyList()
        {
            // Arrange
            var emptyList = new List<double>();

            // Act
            var marketData = new MarketData { RecentPrices = emptyList };

            // Assert
            marketData.RecentPrices.Should().BeEmpty();
        }

        [Fact]
        public void MarketData_RecentPrices_ShouldAcceptNullValue()
        {
            // Act
            var marketData = new MarketData { RecentPrices = null };

            // Assert
            marketData.RecentPrices.Should().BeNull();
        }
    }

    public class EnhancedMarketDataTests
    {
        [Fact]
        public void EnhancedMarketData_ShouldInheritFromMarketData()
        {
            // Act
            var enhancedData = new EnhancedMarketData();

            // Assert
            enhancedData.Should().BeAssignableTo<MarketData>();
        }

        [Fact]
        public void EnhancedMarketData_DefaultConstructor_ShouldInitializeAdditionalProperties()
        {
            // Act
            var enhancedData = new EnhancedMarketData();

            // Assert
            enhancedData.RelatedNews.Should().NotBeNull();
            enhancedData.EconomicData.Should().BeNull();
            enhancedData.Volume.Should().BeNull();
            enhancedData.MarketCap.Should().BeNull();
            enhancedData.DividendYield.Should().BeNull();
        }

        [Fact]
        public void EnhancedMarketData_WithCompleteData_ShouldSetAllProperties()
        {
            // Arrange
            var newsItems = TestDataBuilder.CreateNewsItems("AAPL");
            var economicData = TestDataBuilder.CreateEconomicIndicators();

            // Act
            var enhancedData = TestDataBuilder.CreateEnhancedMarketData("AAPL");

            // Assert
            enhancedData.Symbol.Should().Be("AAPL");
            enhancedData.RelatedNews.Should().NotBeEmpty();
            enhancedData.EconomicData.Should().NotBeNull();
            enhancedData.Volume.Should().BeGreaterThan(0);
            enhancedData.MarketCap.Should().BeGreaterThan(0);
            enhancedData.DividendYield.Should().BeGreaterThan(0);
        }

        [Fact]
        public void EnhancedMarketData_DataSource_ShouldBeSettable()
        {
            // Act
            var enhancedData = new EnhancedMarketData { DataSource = "YahooFinance" };

            // Assert
            enhancedData.DataSource.Should().Be("YahooFinance");
        }

        [Fact]
        public void EnhancedMarketData_CompanyInfo_ShouldBeSettable()
        {
            // Act
            var enhancedData = new EnhancedMarketData
            {
                CompanyName = "Apple Inc.",
                Industry = "Technology",
                Country = "US"
            };

            // Assert
            enhancedData.CompanyName.Should().Be("Apple Inc.");
            enhancedData.Industry.Should().Be("Technology");
            enhancedData.Country.Should().Be("US");
        }
    }

    public class NewsItemTests
    {
        [Fact]
        public void NewsItem_DefaultConstructor_ShouldInitializeProperties()
        {
            // Act
            var newsItem = new NewsItem();

            // Assert
            newsItem.Title.Should().BeNull();
            newsItem.Summary.Should().BeNull();
            newsItem.Url.Should().BeNull();
            newsItem.PublishedAt.Should().Be(default(DateTime));
            newsItem.Source.Should().Be(string.Empty);
            newsItem.SentimentScore.Should().Be(0.0);
            newsItem.Keywords.Should().NotBeNull();
        }

        [Fact]
        public void NewsItem_PropertyAssignment_ShouldWorkCorrectly()
        {
            // Arrange
            var title = "Breaking News";
            var summary = "Important market update";
            var url = "https://example.com/news";
            var publishedAt = DateTime.UtcNow;
            var source = "Financial Times";
            var sentimentScore = 0.8;
            var keywords = new List<string> { "market", "earnings" };

            // Act
            var newsItem = new NewsItem
            {
                Title = title,
                Summary = summary,
                Url = url,
                PublishedAt = publishedAt,
                Source = source,
                SentimentScore = sentimentScore,
                Keywords = keywords
            };

            // Assert
            newsItem.Title.Should().Be(title);
            newsItem.Summary.Should().Be(summary);
            newsItem.Url.Should().Be(url);
            newsItem.PublishedAt.Should().Be(publishedAt);
            newsItem.Source.Should().Be(source);
            newsItem.SentimentScore.Should().Be(sentimentScore);
            newsItem.Keywords.Should().BeEquivalentTo(keywords);
        }
    }
}
