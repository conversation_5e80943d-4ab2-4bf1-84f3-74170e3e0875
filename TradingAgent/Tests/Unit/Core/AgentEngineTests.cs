using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using TradingAgent.Core;
using TradingAgent.Services;
using TradingAgent.Models;
using TradingAgent.Tests.Helpers;
using FluentAssertions;

namespace TradingAgent.Tests.Unit.Core
{
    public class AgentEngineTests
    {
        private readonly Mock<IOpenAIClient> _mockOpenAIClient;
        private readonly Mock<ILogger<AgentEngine>> _mockLogger;
        private readonly AgentEngine _agentEngine;

        public AgentEngineTests()
        {
            _mockOpenAIClient = TestMockFactory.CreateMockOpenAIClient();
            _mockLogger = TestMockFactory.CreateMockLogger<AgentEngine>();
            _agentEngine = new AgentEngine(_mockOpenAIClient.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task RunAsync_WithValidMarketData_ShouldReturnAnalysisResult()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("AAPL");

            // Act
            var result = await _agentEngine.RunAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            
            // Verify the result structure
            var resultType = result.GetType();
            resultType.GetProperty("bias").Should().NotBeNull();
            resultType.GetProperty("confidence").Should().NotBeNull();
            resultType.GetProperty("details").Should().NotBeNull();
            resultType.GetProperty("processingTimeMs").Should().NotBeNull();
            resultType.GetProperty("symbol").Should().NotBeNull();
        }

        [Fact]
        public async Task RunAsync_WithEnhancedMarketData_ShouldIncludeAllAgentAnalysis()
        {
            // Arrange
            var enhancedData = TestDataBuilder.CreateEnhancedMarketData("TSLA");

            // Act
            var result = await _agentEngine.RunAsync(enhancedData);

            // Assert
            result.Should().NotBeNull();
            
            // Extract details array from result
            var resultType = result.GetType();
            var detailsProperty = resultType.GetProperty("details");
            var details = detailsProperty?.GetValue(result) as Array;

            details.Should().NotBeNull();
            details!.Length.Should().Be(6); // 4 AI agents + 2 technical agents
        }

        [Theory]
        [InlineData("AAPL")]
        [InlineData("GOOGL")]
        [InlineData("MSFT")]
        public async Task RunAsync_WithDifferentSymbols_ShouldProcessSuccessfully(string symbol)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData(symbol);

            // Act
            var result = await _agentEngine.RunAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            
            var resultType = result.GetType();
            var symbolProperty = resultType.GetProperty("symbol");
            var resultSymbol = symbolProperty?.GetValue(result) as string;
            
            resultSymbol.Should().Be(symbol);
        }

        [Fact]
        public async Task RunAsync_ShouldCallOpenAIClientForAIAgents()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateEnhancedMarketData("NVDA");

            // Act
            await _agentEngine.RunAsync(marketData);

            // Assert
            // Verify OpenAI client was called for AI-powered agents (4 times)
            _mockOpenAIClient.Verify(
                x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Exactly(4));
        }

        [Fact]
        public async Task RunAsync_ShouldLogAnalysisStart()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("AMD");

            // Act
            await _agentEngine.RunAsync(marketData);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Starting analysis for symbol: AMD")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task RunAsync_ShouldLogAnalysisCompletion()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("INTC");

            // Act
            await _agentEngine.RunAsync(marketData);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Analysis completed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task RunAsync_WithHighRSI_ShouldInfluenceBias()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("OVERBOUGHT");
            marketData.RSI = 85.0; // Overbought condition

            // Act
            var result = await _agentEngine.RunAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            
            var resultType = result.GetType();
            var biasProperty = resultType.GetProperty("bias");
            var bias = biasProperty?.GetValue(result) as string;
            
            // With high RSI, momentum agent should indicate overbought
            bias.Should().NotBeNull();
        }

        [Fact]
        public async Task RunAsync_WithLowRSI_ShouldInfluenceBias()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("OVERSOLD");
            marketData.RSI = 25.0; // Oversold condition

            // Act
            var result = await _agentEngine.RunAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            
            var resultType = result.GetType();
            var biasProperty = resultType.GetProperty("bias");
            var bias = biasProperty?.GetValue(result) as string;
            
            bias.Should().NotBeNull();
        }

        [Fact]
        public async Task RunAsync_ShouldCalculateConfidenceLevel()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateEnhancedMarketData("CONFIDENCE_TEST");

            // Act
            var result = await _agentEngine.RunAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            
            var resultType = result.GetType();
            var confidenceProperty = resultType.GetProperty("confidence");
            var confidence = confidenceProperty?.GetValue(result);
            
            confidence.Should().NotBeNull();
            confidence.Should().BeOfType<double>();
            
            var confidenceValue = (double)confidence!;
            confidenceValue.Should().BeGreaterThan(0.0);
            confidenceValue.Should().BeLessThanOrEqualTo(1.0);
        }

        [Fact]
        public async Task RunAsync_ShouldIncludeProcessingTime()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("TIMING_TEST");

            // Act
            var result = await _agentEngine.RunAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            
            var resultType = result.GetType();
            var processingTimeProperty = resultType.GetProperty("processingTimeMs");
            var processingTime = processingTimeProperty?.GetValue(result);

            processingTime.Should().NotBeNull();
            processingTime.Should().BeOfType<double>();

            var timeValue = (double)processingTime!;
            timeValue.Should().BeGreaterThan(0.0);
        }
    }
}
