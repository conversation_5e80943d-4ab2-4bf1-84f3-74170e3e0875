using Xunit;
using Core;
using FluentAssertions;

namespace TradingAgent.Tests.Unit.Core
{
    public class AgentMessageTests
    {
        [Fact]
        public void AgentMessage_DefaultConstructor_ShouldInitializeProperties()
        {
            // Act
            var message = new AgentMessage();

            // Assert
            message.Sender.Should().BeNull();
            message.Insight.Should().BeNull();
            message.Confidence.Should().Be(0.0);
        }

        [Fact]
        public void AgentMessage_PropertyAssignment_ShouldWorkCorrectly()
        {
            // Arrange
            var sender = "TestAgent";
            var insight = "BUY - Strong fundamentals";
            var confidence = 0.85;

            // Act
            var message = new AgentMessage
            {
                Sender = sender,
                Insight = insight,
                Confidence = confidence
            };

            // Assert
            message.Sender.Should().Be(sender);
            message.Insight.Should().Be(insight);
            message.Confidence.Should().Be(confidence);
        }

        [Theory]
        [InlineData("FundamentalAnalyst")]
        [InlineData("SentimentAnalyst")]
        [InlineData("NewsAnalyst")]
        [InlineData("RiskAnalyst")]
        [InlineData("MomentumAnalyst")]
        [InlineData("SupportResistanceAnalyst")]
        public void AgentMessage_Sender_ShouldAcceptValidAgentNames(string agentName)
        {
            // Act
            var message = new AgentMessage { Sender = agentName };

            // Assert
            message.Sender.Should().Be(agentName);
        }

        [Theory]
        [InlineData("BUY - Strong fundamentals")]
        [InlineData("SELL - Weak performance")]
        [InlineData("HOLD - Neutral outlook")]
        [InlineData("Overbought")]
        [InlineData("Oversold")]
        [InlineData("Near Resistance")]
        [InlineData("Safe Zone")]
        public void AgentMessage_Insight_ShouldAcceptVariousInsights(string insight)
        {
            // Act
            var message = new AgentMessage { Insight = insight };

            // Assert
            message.Insight.Should().Be(insight);
        }

        [Theory]
        [InlineData(0.0)]
        [InlineData(0.5)]
        [InlineData(0.85)]
        [InlineData(1.0)]
        public void AgentMessage_Confidence_ShouldAcceptValidRange(double confidence)
        {
            // Act
            var message = new AgentMessage { Confidence = confidence };

            // Assert
            message.Confidence.Should().Be(confidence);
        }

        [Theory]
        [InlineData(-0.1)]
        [InlineData(1.1)]
        [InlineData(2.0)]
        [InlineData(-1.0)]
        public void AgentMessage_Confidence_ShouldAcceptOutOfRangeValues(double confidence)
        {
            // Note: The class doesn't enforce range validation, so this tests current behavior
            // Act
            var message = new AgentMessage { Confidence = confidence };

            // Assert
            message.Confidence.Should().Be(confidence);
        }

        [Fact]
        public void AgentMessage_WithNullValues_ShouldHandleGracefully()
        {
            // Act
            var message = new AgentMessage
            {
                Sender = null,
                Insight = null,
                Confidence = 0.0
            };

            // Assert
            message.Sender.Should().BeNull();
            message.Insight.Should().BeNull();
            message.Confidence.Should().Be(0.0);
        }

        [Fact]
        public void AgentMessage_WithEmptyStrings_ShouldHandleGracefully()
        {
            // Act
            var message = new AgentMessage
            {
                Sender = "",
                Insight = "",
                Confidence = 0.5
            };

            // Assert
            message.Sender.Should().Be("");
            message.Insight.Should().Be("");
            message.Confidence.Should().Be(0.5);
        }

        [Fact]
        public void AgentMessage_WithLongInsight_ShouldAcceptLongText()
        {
            // Arrange
            var longInsight = new string('A', 1000) + " - This is a very long insight message that contains detailed analysis.";

            // Act
            var message = new AgentMessage { Insight = longInsight };

            // Assert
            message.Insight.Should().Be(longInsight);
            message.Insight.Length.Should().BeGreaterThan(1000);
        }

        [Fact]
        public void AgentMessage_WithSpecialCharacters_ShouldHandleCorrectly()
        {
            // Arrange
            var specialSender = "Agent-1_Test@Domain";
            var specialInsight = "BUY - Price target: $150.50 (↑15%) with 95% confidence! 🚀📈";

            // Act
            var message = new AgentMessage
            {
                Sender = specialSender,
                Insight = specialInsight,
                Confidence = 0.95
            };

            // Assert
            message.Sender.Should().Be(specialSender);
            message.Insight.Should().Be(specialInsight);
            message.Confidence.Should().Be(0.95);
        }

        [Theory]
        [InlineData(double.MinValue)]
        [InlineData(double.MaxValue)]
        [InlineData(double.NaN)]
        [InlineData(double.PositiveInfinity)]
        [InlineData(double.NegativeInfinity)]
        public void AgentMessage_Confidence_ShouldHandleExtremeValues(double confidence)
        {
            // Act
            var message = new AgentMessage { Confidence = confidence };

            // Assert
            message.Confidence.Should().Be(confidence);
        }

        [Fact]
        public void AgentMessage_MultipleInstances_ShouldBeIndependent()
        {
            // Arrange & Act
            var message1 = new AgentMessage
            {
                Sender = "Agent1",
                Insight = "BUY",
                Confidence = 0.8
            };

            var message2 = new AgentMessage
            {
                Sender = "Agent2",
                Insight = "SELL",
                Confidence = 0.6
            };

            // Assert
            message1.Sender.Should().Be("Agent1");
            message1.Insight.Should().Be("BUY");
            message1.Confidence.Should().Be(0.8);

            message2.Sender.Should().Be("Agent2");
            message2.Insight.Should().Be("SELL");
            message2.Confidence.Should().Be(0.6);

            // Verify they are independent
            message1.Sender.Should().NotBe(message2.Sender);
            message1.Insight.Should().NotBe(message2.Insight);
            message1.Confidence.Should().NotBe(message2.Confidence);
        }

        [Fact]
        public void AgentMessage_ToString_ShouldProvideUsefulRepresentation()
        {
            // Arrange
            var message = new AgentMessage
            {
                Sender = "TestAgent",
                Insight = "BUY - Strong outlook",
                Confidence = 0.75
            };

            // Act
            var stringRepresentation = message.ToString();

            // Assert
            stringRepresentation.Should().NotBeNullOrEmpty();
            // Note: This assumes ToString() is overridden. If not, it will return the type name.
        }
    }
}
