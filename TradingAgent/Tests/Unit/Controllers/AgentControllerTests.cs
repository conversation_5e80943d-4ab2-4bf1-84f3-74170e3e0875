using Xunit;
using Moq;
using Microsoft.AspNetCore.Mvc;
using TradingAgent.Controllers;
using TradingAgent.Core;
using TradingAgent.Services;
using TradingAgent.Models;
using TradingAgent.Tests.Helpers;
using FluentAssertions;

namespace TradingAgent.Tests.Unit.Controllers
{
    public class AgentControllerTests
    {
        private readonly Mock<IAgentEngine> _mockAgentEngine;
        private readonly Mock<IDataAggregationService> _mockDataService;
        private readonly AgentController _controller;

        public AgentControllerTests()
        {
            _mockAgentEngine = TestMockFactory.CreateMockAgentEngine();
            _mockDataService = TestMockFactory.CreateMockDataAggregationService();
            _controller = new AgentController(_mockAgentEngine.Object, _mockDataService.Object);
        }

        [Fact]
        public async Task Analyze_WithValidMarketData_ShouldReturnOkResult()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("AAPL");
            var analysisResult = new { bias = "BUY", confidence = 0.8, symbol = "AAPL" };
            
            _mockAgentEngine.Setup(x => x.RunAsync(It.IsAny<MarketData>()))
                .ReturnsAsync(analysisResult);

            // Act
            var result = await _controller.Analyze(marketData);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().Be(analysisResult);
        }

        [Fact]
        public async Task Analyze_ShouldCallAgentEngine()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("TSLA");
            var analysisResult = new { bias = "HOLD", confidence = 0.6 };
            
            _mockAgentEngine.Setup(x => x.RunAsync(marketData))
                .ReturnsAsync(analysisResult);

            // Act
            await _controller.Analyze(marketData);

            // Assert
            _mockAgentEngine.Verify(x => x.RunAsync(marketData), Times.Once);
        }

        [Fact]
        public async Task AnalyzeSymbol_WithValidSymbol_ShouldReturnOkResult()
        {
            // Arrange
            var symbol = "GOOGL";
            var enhancedData = TestDataBuilder.CreateEnhancedMarketData(symbol);
            var dataResponse = TestDataBuilder.CreateSuccessResponse(enhancedData);
            var analysisResult = new { bias = "BUY", confidence = 0.9 };

            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(dataResponse);
            
            _mockAgentEngine.Setup(x => x.RunAsync(It.IsAny<MarketData>()))
                .ReturnsAsync(analysisResult);

            // Act
            var result = await _controller.AnalyzeSymbol(symbol);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().NotBeNull();
        }

        [Fact]
        public async Task AnalyzeSymbol_ShouldCreateCorrectMarketDataRequest()
        {
            // Arrange
            var symbol = "MSFT";
            var includeNews = true;
            var includeEconomicData = false;
            var newsLookbackDays = 14;
            var preferredDataSource = "AlphaVantage";
            var forceRefresh = true;

            MarketDataRequest capturedRequest = null!;
            
            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .Callback<MarketDataRequest, CancellationToken>((request, ct) => capturedRequest = request)
                .ReturnsAsync(TestDataBuilder.CreateSuccessResponse(TestDataBuilder.CreateEnhancedMarketData(symbol)));

            // Act
            await _controller.AnalyzeSymbol(symbol, includeNews, includeEconomicData, newsLookbackDays, preferredDataSource, forceRefresh);

            // Assert
            capturedRequest.Should().NotBeNull();
            capturedRequest.Symbol.Should().Be(symbol.ToUpper());
            capturedRequest.IncludeNews.Should().Be(includeNews);
            capturedRequest.IncludeEconomicData.Should().Be(includeEconomicData);
            capturedRequest.NewsLookbackDays.Should().Be(newsLookbackDays);
            capturedRequest.PreferredDataSource.Should().Be(preferredDataSource);
            capturedRequest.ForceRefresh.Should().Be(forceRefresh);
        }

        [Fact]
        public async Task AnalyzeSymbol_WithDefaultParameters_ShouldUseDefaults()
        {
            // Arrange
            var symbol = "NVDA";
            MarketDataRequest capturedRequest = null!;
            
            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .Callback<MarketDataRequest, CancellationToken>((request, ct) => capturedRequest = request)
                .ReturnsAsync(TestDataBuilder.CreateSuccessResponse(TestDataBuilder.CreateEnhancedMarketData(symbol)));

            // Act
            await _controller.AnalyzeSymbol(symbol);

            // Assert
            capturedRequest.Should().NotBeNull();
            capturedRequest.IncludeNews.Should().BeTrue();
            capturedRequest.IncludeEconomicData.Should().BeTrue();
            capturedRequest.NewsLookbackDays.Should().Be(7);
            capturedRequest.PreferredDataSource.Should().BeNull();
            capturedRequest.ForceRefresh.Should().BeFalse();
        }

        [Fact]
        public async Task AnalyzeSymbol_WhenDataFetchFails_ShouldReturnBadRequest()
        {
            // Arrange
            var symbol = "INVALID";
            var errorResponse = TestDataBuilder.CreateErrorResponse<EnhancedMarketData>("Symbol not found");
            
            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(errorResponse);

            // Act
            var result = await _controller.AnalyzeSymbol(symbol);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().NotBeNull();
            
            var errorObject = badRequestResult.Value;
            var errorProperty = errorObject!.GetType().GetProperty("error");
            errorProperty!.GetValue(errorObject).Should().Be("Failed to fetch market data");
        }

        [Fact]
        public async Task AnalyzeSymbol_WhenDataIsNull_ShouldReturnBadRequest()
        {
            // Arrange
            var symbol = "NULL_DATA";
            var response = new DataProviderResponse<EnhancedMarketData>
            {
                Success = true,
                Data = null,
                DataSource = "TestProvider"
            };
            
            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.AnalyzeSymbol(symbol);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
        }

        [Fact]
        public async Task AnalyzeSymbol_ShouldIncludeDataMetadata()
        {
            // Arrange
            var symbol = "META";
            var enhancedData = TestDataBuilder.CreateEnhancedMarketData(symbol);
            var dataResponse = TestDataBuilder.CreateSuccessResponse(enhancedData, "YahooFinance");
            dataResponse.FromCache = true;
            dataResponse.ResponseTime = TimeSpan.FromMilliseconds(150);
            
            var analysisResult = new { bias = "SELL", confidence = 0.7 };

            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(dataResponse);
            
            _mockAgentEngine.Setup(x => x.RunAsync(It.IsAny<MarketData>()))
                .ReturnsAsync(analysisResult);

            // Act
            var result = await _controller.AnalyzeSymbol(symbol);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            var responseValue = okResult!.Value;
            
            var dataMetadataProperty = responseValue!.GetType().GetProperty("dataMetadata");
            dataMetadataProperty.Should().NotBeNull();
            
            var dataMetadata = dataMetadataProperty!.GetValue(responseValue);
            var dataSourceProperty = dataMetadata!.GetType().GetProperty("dataSource");
            dataSourceProperty!.GetValue(dataMetadata).Should().Be("YahooFinance");
        }

        [Fact]
        public async Task AnalyzeSymbol_ShouldConvertSymbolToUpperCase()
        {
            // Arrange
            var symbol = "aapl";
            MarketDataRequest capturedRequest = null!;
            
            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .Callback<MarketDataRequest, CancellationToken>((request, ct) => capturedRequest = request)
                .ReturnsAsync(TestDataBuilder.CreateSuccessResponse(TestDataBuilder.CreateEnhancedMarketData("AAPL")));

            // Act
            await _controller.AnalyzeSymbol(symbol);

            // Assert
            capturedRequest.Symbol.Should().Be("AAPL");
        }

        [Fact]
        public async Task AnalyzeSymbol_WhenExceptionThrown_ShouldReturnInternalServerError()
        {
            // Arrange
            var symbol = "ERROR";
            _mockDataService.Setup(x => x.GetCompleteMarketDataAsync(It.IsAny<MarketDataRequest>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Service error"));

            // Act
            var result = await _controller.AnalyzeSymbol(symbol);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            statusCodeResult.StatusCode.Should().Be(500);

            var responseValue = statusCodeResult.Value;
            responseValue.Should().NotBeNull();

            var errorProperty = responseValue!.GetType().GetProperty("error");
            errorProperty.Should().NotBeNull();
            errorProperty!.GetValue(responseValue).Should().Be("Internal server error");
        }
    }
}
