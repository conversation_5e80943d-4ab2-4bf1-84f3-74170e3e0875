using Xunit;
using Moq;
using TradingAgent.Agents;
using TradingAgent.Services;
using TradingAgent.Tests.Helpers;
using FluentAssertions;

namespace TradingAgent.Tests.Unit.Agents
{
    public class FundamentalAnalystTests
    {
        private readonly Mock<IOpenAIClient> _mockOpenAIClient;
        private readonly FundamentalAnalysisAgent _agent;

        public FundamentalAnalystTests()
        {
            _mockOpenAIClient = TestMockFactory.CreateMockOpenAIClient();
            _agent = new FundamentalAnalysisAgent(_mockOpenAIClient.Object);
        }

        [Fact]
        public void Name_ShouldReturnCorrectAgentName()
        {
            // Act & Assert
            _agent.Name.Should().Be("FundamentalAnalyst");
        }

        [Fact]
        public async Task AnalyzeAsync_WithValidMarketData_ShouldCallOpenAIClient()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("AAPL");
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync("BUY - Strong fundamentals with PE ratio of 25.5 indicating fair valuation.");

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            _mockOpenAIClient.Verify(
                x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Once);
            
            result.Should().NotBeNull();
            result.Sender.Should().Be("FundamentalAnalyst");
            result.Insight.Should().Contain("BUY");
        }

        [Fact]
        public async Task AnalyzeAsync_ShouldGetModelForAgent()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("TSLA");

            // Act
            await _agent.AnalyzeAsync(marketData);

            // Assert
            _mockOpenAIClient.Verify(
                x => x.GetModelForAgent("FundamentalAnalyst"),
                Times.Once);
        }

        [Fact]
        public async Task AnalyzeAsync_ShouldIncludeSymbolInPrompt()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("GOOGL");
            string capturedPrompt = "";
            
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .Callback<string, string, string>((userPrompt, model, systemPrompt) => capturedPrompt = userPrompt)
                .ReturnsAsync("HOLD - Neutral outlook based on current fundamentals.");

            // Act
            await _agent.AnalyzeAsync(marketData);

            // Assert
            capturedPrompt.Should().Contain("GOOGL");
        }

        [Fact]
        public async Task AnalyzeAsync_ShouldIncludeFinancialDataInPrompt()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("MSFT");
            marketData.Price = 350.75;
            marketData.PERatio = 28.5;
            marketData.Sector = "Technology";
            
            string capturedPrompt = "";
            
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .Callback<string, string, string>((userPrompt, model, systemPrompt) => capturedPrompt = userPrompt)
                .ReturnsAsync("BUY - Strong technology sector fundamentals.");

            // Act
            await _agent.AnalyzeAsync(marketData);

            // Assert
            capturedPrompt.Should().Contain("$350.75");
            capturedPrompt.Should().Contain("28.5");
            capturedPrompt.Should().Contain("Technology");
        }

        [Fact]
        public async Task AnalyzeAsync_ShouldIncludeNewsInPrompt()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("NVDA");
            marketData.RawNews = "NVIDIA reports record quarterly earnings with strong AI chip demand.";
            
            string capturedPrompt = "";
            
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .Callback<string, string, string>((userPrompt, model, systemPrompt) => capturedPrompt = userPrompt)
                .ReturnsAsync("BUY - Positive earnings report supports bullish outlook.");

            // Act
            await _agent.AnalyzeAsync(marketData);

            // Assert
            capturedPrompt.Should().Contain("NVIDIA reports record quarterly earnings");
        }

        [Theory]
        [InlineData("BUY - Strong fundamentals", 0.8)]
        [InlineData("SELL - Weak performance indicators", 0.7)]
        [InlineData("HOLD - Mixed signals in the data", 0.6)]
        public async Task AnalyzeAsync_ShouldReturnCorrectInsightAndConfidence(string openAIResponse, double expectedMinConfidence)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("TEST");
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(openAIResponse);

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("FundamentalAnalyst");
            result.Insight.Should().Be(openAIResponse);
            result.Confidence.Should().BeGreaterThanOrEqualTo(expectedMinConfidence);
        }

        [Fact]
        public async Task AnalyzeAsync_WithEnhancedMarketData_ShouldIncludeAdditionalMetrics()
        {
            // Arrange
            var enhancedData = TestDataBuilder.CreateEnhancedMarketData("ENHANCED");
            string capturedPrompt = "";
            
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .Callback<string, string, string>((userPrompt, model, systemPrompt) => capturedPrompt = userPrompt)
                .ReturnsAsync("BUY - Comprehensive analysis shows strong fundamentals.");

            // Act
            await _agent.AnalyzeAsync(enhancedData);

            // Assert
            capturedPrompt.Should().Contain("Market Cap:");
            capturedPrompt.Should().Contain("EPS:");
            capturedPrompt.Should().Contain("ROE:");
        }

        [Fact]
        public async Task AnalyzeAsync_WithNullNews_ShouldHandleGracefully()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("NO_NEWS");
            marketData.RawNews = null;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("FundamentalAnalyst");
            _mockOpenAIClient.Verify(
                x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Once);
        }

        [Fact]
        public async Task AnalyzeAsync_WithEmptyNews_ShouldHandleGracefully()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("EMPTY_NEWS");
            marketData.RawNews = "";

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("FundamentalAnalyst");
        }

        [Fact]
        public async Task AnalyzeAsync_ShouldUseCorrectSystemPrompt()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("SYSTEM_TEST");
            string capturedSystemPrompt = "";
            
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .Callback<string, string, string>((userPrompt, model, systemPrompt) => capturedSystemPrompt = systemPrompt)
                .ReturnsAsync("Analysis complete.");

            // Act
            await _agent.AnalyzeAsync(marketData);

            // Assert
            capturedSystemPrompt.Should().Contain("professional fundamental analyst");
            capturedSystemPrompt.Should().Contain("BUY/SELL/HOLD recommendation");
            capturedSystemPrompt.Should().Contain("Risk factors");
        }

        [Fact]
        public async Task AnalyzeAsync_WhenOpenAIThrows_ShouldPropagateException()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("ERROR_TEST");
            _mockOpenAIClient.Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new InvalidOperationException("OpenAI API error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _agent.AnalyzeAsync(marketData));
        }
    }
}
