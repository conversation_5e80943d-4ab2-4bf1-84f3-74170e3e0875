using Xunit;
using TradingAgent.Agents;
using TradingAgent.Tests.Helpers;
using FluentAssertions;

namespace TradingAgent.Tests.Unit.Agents
{
    public class MomentumAnalystTests
    {
        private readonly MomentumAnalysisAgent _agent;

        public MomentumAnalystTests()
        {
            _agent = new MomentumAnalysisAgent();
        }

        [Fact]
        public void Name_ShouldReturnCorrectAgentName()
        {
            // Act & Assert
            _agent.Name.Should().Be("MomentumAnalyst");
        }

        [Theory]
        [InlineData(75.0, "Overbought", 0.9)]
        [InlineData(85.0, "Overbought", 0.9)]
        [InlineData(95.0, "Overbought", 0.9)]
        public async Task AnalyzeAsync_WithHighRSI_ShouldReturnOverbought(double rsi, string expectedInsight, double expectedConfidence)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("AAPL");
            marketData.RSI = rsi;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("MomentumAnalyst");
            result.Insight.Should().Be(expectedInsight);
            result.Confidence.Should().Be(expectedConfidence);
        }

        [Theory]
        [InlineData(25.0, "Oversold", 0.9)]
        [InlineData(15.0, "Oversold", 0.9)]
        [InlineData(5.0, "Oversold", 0.9)]
        public async Task AnalyzeAsync_WithLowRSI_ShouldReturnOversold(double rsi, string expectedInsight, double expectedConfidence)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("TSLA");
            marketData.RSI = rsi;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("MomentumAnalyst");
            result.Insight.Should().Be(expectedInsight);
            result.Confidence.Should().Be(expectedConfidence);
        }

        [Theory]
        [InlineData(30.1, "Neutral", 0.5)]
        [InlineData(45.0, "Neutral", 0.5)]
        [InlineData(55.0, "Neutral", 0.5)]
        [InlineData(69.9, "Neutral", 0.5)]
        public async Task AnalyzeAsync_WithNeutralRSI_ShouldReturnNeutral(double rsi, string expectedInsight, double expectedConfidence)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("GOOGL");
            marketData.RSI = rsi;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("MomentumAnalyst");
            result.Insight.Should().Be(expectedInsight);
            result.Confidence.Should().Be(expectedConfidence);
        }

        [Theory]
        [InlineData(30.0, "Oversold")]
        [InlineData(70.0, "Overbought")]
        public async Task AnalyzeAsync_WithBoundaryRSI_ShouldReturnCorrectInsight(double rsi, string expectedInsight)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("MSFT");
            marketData.RSI = rsi;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Insight.Should().Be(expectedInsight);
            result.Confidence.Should().Be(0.9);
        }

        [Fact]
        public async Task AnalyzeAsync_ShouldCompleteQuickly()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("NVDA");
            var startTime = DateTime.UtcNow;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);
            var endTime = DateTime.UtcNow;

            // Assert
            result.Should().NotBeNull();
            (endTime - startTime).TotalMilliseconds.Should().BeLessThan(100); // Should be very fast
        }

        [Fact]
        public async Task AnalyzeAsync_WithEnhancedMarketData_ShouldStillWork()
        {
            // Arrange
            var enhancedData = TestDataBuilder.CreateEnhancedMarketData("AMD");
            enhancedData.RSI = 80.0;

            // Act
            var result = await _agent.AnalyzeAsync(enhancedData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("MomentumAnalyst");
            result.Insight.Should().Be("Overbought");
            result.Confidence.Should().Be(0.9);
        }

        [Theory]
        [InlineData(0.0)]
        [InlineData(100.0)]
        [InlineData(-10.0)]
        [InlineData(110.0)]
        public async Task AnalyzeAsync_WithExtremeRSIValues_ShouldHandleGracefully(double rsi)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("EXTREME");
            marketData.RSI = rsi;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("MomentumAnalyst");
            result.Insight.Should().NotBeNullOrEmpty();
            result.Confidence.Should().BeGreaterThan(0.0);
            result.Confidence.Should().BeLessThanOrEqualTo(1.0);
        }
    }
}
