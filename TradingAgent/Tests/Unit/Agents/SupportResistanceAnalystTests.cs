using Xunit;
using TradingAgent.Agents;
using TradingAgent.Tests.Helpers;
using FluentAssertions;

namespace TradingAgent.Tests.Unit.Agents
{
    public class SupportResistanceAnalystTests
    {
        private readonly SupportResistanceAgent _agent;

        public SupportResistanceAnalystTests()
        {
            _agent = new SupportResistanceAgent();
        }

        [Fact]
        public void Name_ShouldReturnCorrectAgentName()
        {
            // Act & Assert
            _agent.Name.Should().Be("SupportResistanceAnalyst");
        }

        [Fact]
        public async Task AnalyzeAsync_WithPriceNearResistance_ShouldReturnNearResistance()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("AAPL");
            marketData.RecentPrices = new List<double> { 140.0, 145.0, 150.0, 155.0, 160.0 };
            marketData.Price = 158.0; // Near the high of 160.0

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("SupportResistanceAnalyst");
            result.Insight.Should().Be("Near Resistance");
            result.Confidence.Should().Be(0.8);
        }

        [Fact]
        public async Task AnalyzeAsync_WithPriceInSafeZone_ShouldReturnSafeZone()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("TSLA");
            marketData.RecentPrices = new List<double> { 140.0, 145.0, 150.0, 155.0, 160.0 };
            marketData.Price = 145.0; // Well below resistance

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("SupportResistanceAnalyst");
            result.Insight.Should().Be("Safe Zone");
            result.Confidence.Should().Be(0.6);
        }

        [Theory]
        [InlineData(new double[] { 100.0, 105.0, 110.0, 115.0, 120.0 }, 118.0, "Near Resistance")]
        [InlineData(new double[] { 100.0, 105.0, 110.0, 115.0, 120.0 }, 108.0, "Safe Zone")]
        [InlineData(new double[] { 50.0, 55.0, 60.0, 65.0, 70.0 }, 68.0, "Near Resistance")]
        [InlineData(new double[] { 50.0, 55.0, 60.0, 65.0, 70.0 }, 55.0, "Safe Zone")]
        public async Task AnalyzeAsync_WithVariousPriceRanges_ShouldCalculateCorrectly(double[] recentPrices, double currentPrice, string expectedInsight)
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("TEST");
            marketData.RecentPrices = recentPrices.ToList();
            marketData.Price = currentPrice;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Insight.Should().Be(expectedInsight);
        }

        [Fact]
        public async Task AnalyzeAsync_WithSamePrices_ShouldHandleGracefully()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("FLAT");
            marketData.RecentPrices = new List<double> { 100.0, 100.0, 100.0, 100.0, 100.0 };
            marketData.Price = 100.0;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("SupportResistanceAnalyst");
            result.Insight.Should().NotBeNullOrEmpty();
            result.Confidence.Should().BeGreaterThan(0.0);
        }

        [Fact]
        public async Task AnalyzeAsync_WithSinglePrice_ShouldHandleGracefully()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("SINGLE");
            marketData.RecentPrices = new List<double> { 150.0 };
            marketData.Price = 150.0;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("SupportResistanceAnalyst");
            result.Insight.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task AnalyzeAsync_WithEmptyPrices_ShouldHandleGracefully()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("EMPTY");
            marketData.RecentPrices = new List<double>();
            marketData.Price = 150.0;

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _agent.AnalyzeAsync(marketData));
        }

        [Fact]
        public async Task AnalyzeAsync_WithLargePriceRange_ShouldCalculateCorrectly()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("VOLATILE");
            marketData.RecentPrices = new List<double> { 50.0, 75.0, 100.0, 125.0, 200.0 };
            marketData.Price = 185.0; // Near resistance (200 - 15 = 185)

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Insight.Should().Be("Near Resistance");
            result.Confidence.Should().Be(0.8);
        }

        [Fact]
        public async Task AnalyzeAsync_WithNegativePrices_ShouldHandleGracefully()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("NEGATIVE");
            marketData.RecentPrices = new List<double> { -10.0, -5.0, 0.0, 5.0, 10.0 };
            marketData.Price = 8.0;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);

            // Assert
            result.Should().NotBeNull();
            result.Sender.Should().Be("SupportResistanceAnalyst");
            result.Insight.Should().Be("Near Resistance");
            result.Confidence.Should().Be(0.8);
        }

        [Fact]
        public async Task AnalyzeAsync_ShouldCompleteQuickly()
        {
            // Arrange
            var marketData = TestDataBuilder.CreateBasicMarketData("SPEED");
            var startTime = DateTime.UtcNow;

            // Act
            var result = await _agent.AnalyzeAsync(marketData);
            var endTime = DateTime.UtcNow;

            // Assert
            result.Should().NotBeNull();
            (endTime - startTime).TotalMilliseconds.Should().BeLessThan(100);
        }

        [Fact]
        public async Task AnalyzeAsync_WithEnhancedMarketData_ShouldWork()
        {
            // Arrange
            var enhancedData = TestDataBuilder.CreateEnhancedMarketData("ENHANCED");
            enhancedData.RecentPrices = new List<double> { 140.0, 145.0, 150.0, 155.0, 160.0 };
            enhancedData.Price = 158.0;

            // Act
            var result = await _agent.AnalyzeAsync(enhancedData);

            // Assert
            result.Should().NotBeNull();
            result.Insight.Should().Be("Near Resistance");
            result.Confidence.Should().Be(0.8);
        }
    }
}
