#!/bin/bash

# Test Validation Script
# Validates the test project structure and dependencies

echo "🔍 TradingAgent Test Validation"
echo "==============================="

# Check if we're in the correct directory
if [ ! -f "TradingAgent.Tests.csproj" ]; then
    echo "❌ TradingAgent.Tests.csproj not found. Please run this script from the Tests directory."
    exit 1
fi

echo "✅ Test project file found"

# Count test files
UNIT_TESTS=$(find Unit -name "*.cs" 2>/dev/null | wc -l)
INTEGRATION_TESTS=$(find Integration -name "*.cs" 2>/dev/null | wc -l)
HELPER_FILES=$(find Helpers -name "*.cs" 2>/dev/null | wc -l)

echo "📊 Test Statistics:"
echo "   Unit Tests: $UNIT_TESTS files"
echo "   Integration Tests: $INTEGRATION_TESTS files"
echo "   Helper Files: $HELPER_FILES files"

# Check for required test categories
echo ""
echo "🧪 Test Categories:"

if [ -d "Unit/Agents" ]; then
    AGENT_TESTS=$(find Unit/Agents -name "*.cs" | wc -l)
    echo "   ✅ Agent Tests: $AGENT_TESTS files"
else
    echo "   ❌ Agent Tests: Missing"
fi

if [ -d "Unit/Controllers" ]; then
    CONTROLLER_TESTS=$(find Unit/Controllers -name "*.cs" | wc -l)
    echo "   ✅ Controller Tests: $CONTROLLER_TESTS files"
else
    echo "   ❌ Controller Tests: Missing"
fi

if [ -d "Unit/Services" ]; then
    SERVICE_TESTS=$(find Unit/Services -name "*.cs" | wc -l)
    echo "   ✅ Service Tests: $SERVICE_TESTS files"
else
    echo "   ❌ Service Tests: Missing"
fi

if [ -d "Unit/Core" ]; then
    CORE_TESTS=$(find Unit/Core -name "*.cs" | wc -l)
    echo "   ✅ Core Tests: $CORE_TESTS files"
else
    echo "   ❌ Core Tests: Missing"
fi

if [ -d "Unit/Models" ]; then
    MODEL_TESTS=$(find Unit/Models -name "*.cs" | wc -l)
    echo "   ✅ Model Tests: $MODEL_TESTS files"
else
    echo "   ❌ Model Tests: Missing"
fi

# Check for helper files
echo ""
echo "🛠️ Test Utilities:"

if [ -f "Helpers/TestDataBuilder.cs" ]; then
    echo "   ✅ TestDataBuilder found"
else
    echo "   ❌ TestDataBuilder missing"
fi

if [ -f "Helpers/MockFactory.cs" ]; then
    echo "   ✅ MockFactory found"
else
    echo "   ❌ MockFactory missing"
fi

# Check for configuration files
echo ""
echo "⚙️ Configuration:"

if [ -f "TestData/appsettings.Test.json" ]; then
    echo "   ✅ Test configuration found"
else
    echo "   ❌ Test configuration missing"
fi

if [ -f "coverlet.runsettings" ]; then
    echo "   ✅ Coverage settings found"
else
    echo "   ❌ Coverage settings missing"
fi

# Check for test runner
if [ -f "run-tests.sh" ] && [ -x "run-tests.sh" ]; then
    echo "   ✅ Test runner script found and executable"
else
    echo "   ❌ Test runner script missing or not executable"
fi

# Summary
echo ""
echo "📋 Summary:"
TOTAL_TESTS=$((UNIT_TESTS + INTEGRATION_TESTS))
echo "   Total Test Files: $TOTAL_TESTS"
echo "   Helper Files: $HELPER_FILES"

if [ $TOTAL_TESTS -gt 0 ]; then
    echo "   ✅ Test suite appears to be properly structured"
else
    echo "   ❌ No test files found"
fi

echo ""
echo "🚀 Next Steps:"
echo "   1. Run: ./run-tests.sh"
echo "   2. Or: dotnet test"
echo "   3. For coverage: ./run-tests.sh report"
