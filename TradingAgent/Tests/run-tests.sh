#!/bin/bash

# TradingAgent Test Runner Script
# This script runs all tests for the TradingAgent project

set -e  # Exit on any error

echo "🧪 TradingAgent Test Suite Runner"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "TradingAgent.Tests.csproj" ]; then
    print_error "TradingAgent.Tests.csproj not found. Please run this script from the Tests directory."
    exit 1
fi

print_status "Starting test execution..."

# Restore packages
print_status "Restoring NuGet packages..."
dotnet restore

if [ $? -eq 0 ]; then
    print_success "Package restoration completed"
else
    print_error "Package restoration failed"
    exit 1
fi

# Build the test project
print_status "Building test project..."
dotnet build --no-restore

if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

# Run all tests with detailed output
print_status "Running all tests..."
echo ""

# Run tests with coverage and detailed output
dotnet test \
    --no-build \
    --verbosity normal \
    --logger "console;verbosity=detailed" \
    --collect:"XPlat Code Coverage" \
    --results-directory ./TestResults \
    --settings coverlet.runsettings

TEST_EXIT_CODE=$?

echo ""
if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "All tests passed! ✅"
else
    print_error "Some tests failed! ❌"
fi

# Run specific test categories if requested
if [ "$1" = "unit" ]; then
    print_status "Running only unit tests..."
    dotnet test --filter "Category=Unit" --no-build --verbosity normal
elif [ "$1" = "integration" ]; then
    print_status "Running only integration tests..."
    dotnet test --filter "Category=Integration" --no-build --verbosity normal
elif [ "$1" = "agents" ]; then
    print_status "Running only agent tests..."
    dotnet test --filter "FullyQualifiedName~Agents" --no-build --verbosity normal
elif [ "$1" = "services" ]; then
    print_status "Running only service tests..."
    dotnet test --filter "FullyQualifiedName~Services" --no-build --verbosity normal
elif [ "$1" = "controllers" ]; then
    print_status "Running only controller tests..."
    dotnet test --filter "FullyQualifiedName~Controllers" --no-build --verbosity normal
fi

# Generate test report if requested
if [ "$2" = "report" ] || [ "$1" = "report" ]; then
    print_status "Generating test coverage report..."
    
    # Check if reportgenerator is installed
    if ! command -v reportgenerator &> /dev/null; then
        print_warning "reportgenerator not found. Installing..."
        dotnet tool install -g dotnet-reportgenerator-globaltool
    fi
    
    # Generate HTML report
    reportgenerator \
        -reports:"./TestResults/*/coverage.cobertura.xml" \
        -targetdir:"./TestResults/CoverageReport" \
        -reporttypes:Html
    
    print_success "Coverage report generated in ./TestResults/CoverageReport/"
fi

# Display test summary
echo ""
echo "📊 Test Summary"
echo "==============="
print_status "Test results saved to: ./TestResults/"

if [ -d "./TestResults/CoverageReport" ]; then
    print_status "Coverage report available at: ./TestResults/CoverageReport/index.html"
fi

# Display usage information
if [ "$1" = "help" ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo ""
    echo "Usage: ./run-tests.sh [category] [options]"
    echo ""
    echo "Categories:"
    echo "  unit         - Run only unit tests"
    echo "  integration  - Run only integration tests"
    echo "  agents       - Run only agent tests"
    echo "  services     - Run only service tests"
    echo "  controllers  - Run only controller tests"
    echo ""
    echo "Options:"
    echo "  report       - Generate coverage report"
    echo "  help         - Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./run-tests.sh                    # Run all tests"
    echo "  ./run-tests.sh unit               # Run only unit tests"
    echo "  ./run-tests.sh integration report # Run integration tests and generate report"
    echo "  ./run-tests.sh report             # Run all tests and generate report"
fi

exit $TEST_EXIT_CODE
