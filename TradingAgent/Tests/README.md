# TradingAgent Test Suite

This directory contains comprehensive tests for the TradingAgent project, covering all major components and functionality.

## 📁 Test Structure

```
Tests/
├── Unit/                           # Unit tests for individual components
│   ├── Agents/                     # Tests for all agent implementations
│   │   ├── FundamentalAnalystTests.cs
│   │   ├── SentimentAnalystTests.cs
│   │   ├── MomentumAnalystTests.cs
│   │   ├── SupportResistanceAnalystTests.cs
│   │   └── ...
│   ├── Controllers/                # Tests for API controllers
│   │   └── AgentControllerTests.cs
│   ├── Services/                   # Tests for service classes
│   │   ├── DataAggregationServiceTests.cs
│   │   ├── YahooFinanceProviderTests.cs
│   │   └── ...
│   ├── Core/                       # Tests for core components
│   │   ├── AgentEngineTests.cs
│   │   └── AgentMessageTests.cs
│   ├── Models/                     # Tests for data models
│   │   └── MarketDataTests.cs
│   └── Config/                     # Tests for configuration classes
│       └── DataProviderSettingsTests.cs
├── Integration/                    # Integration tests
│   └── AgentControllerIntegrationTests.cs
├── Helpers/                        # Test utilities and helpers
│   ├── TestDataBuilder.cs          # Factory for creating test data
│   └── MockFactory.cs              # Factory for creating mocks
├── TestData/                       # Test configuration and data files
│   └── appsettings.Test.json
├── TradingAgent.Tests.csproj       # Test project file
├── run-tests.sh                    # Test runner script
├── coverlet.runsettings           # Coverage settings
└── README.md                       # This file
```

## 🧪 Test Categories

### Unit Tests
- **Agents**: Tests for all AI-powered and technical analysis agents
- **Controllers**: Tests for API endpoints and request/response handling
- **Services**: Tests for data providers, aggregation services, and OpenAI client
- **Core**: Tests for the agent engine and core messaging system
- **Models**: Tests for data models and DTOs
- **Config**: Tests for configuration classes and settings

### Integration Tests
- **API Integration**: End-to-end tests for API endpoints
- **Service Integration**: Tests for service interactions and data flow

## 🚀 Running Tests

### Prerequisites
- .NET 10.0 SDK
- All project dependencies restored

### Quick Start
```bash
# Navigate to the Tests directory
cd Tests

# Run all tests
./run-tests.sh

# Or use dotnet directly
dotnet test
```

### Test Categories
```bash
# Run only unit tests
./run-tests.sh unit

# Run only integration tests
./run-tests.sh integration

# Run specific component tests
./run-tests.sh agents      # Agent tests only
./run-tests.sh services    # Service tests only
./run-tests.sh controllers # Controller tests only
```

### Coverage Reports
```bash
# Run tests and generate coverage report
./run-tests.sh report

# Run specific tests with coverage
./run-tests.sh unit report
```

### Manual Test Execution
```bash
# Restore packages
dotnet restore

# Build tests
dotnet build

# Run all tests with detailed output
dotnet test --verbosity normal

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test class
dotnet test --filter "ClassName=AgentEngineTests"

# Run specific test method
dotnet test --filter "MethodName=RunAsync_WithValidMarketData_ShouldReturnAnalysisResult"
```

## 📊 Test Coverage

The test suite aims for comprehensive coverage of:

- ✅ **Agent Logic**: All agent implementations (AI and technical)
- ✅ **API Endpoints**: All controller actions and error scenarios
- ✅ **Data Services**: Provider implementations and aggregation logic
- ✅ **Core Engine**: Agent orchestration and result processing
- ✅ **Models**: Data structures and validation
- ✅ **Configuration**: Settings and provider configurations
- ✅ **Error Handling**: Exception scenarios and fallback mechanisms
- ✅ **Integration**: End-to-end API workflows

## 🛠️ Test Utilities

### TestDataBuilder
Provides factory methods for creating test data:
```csharp
var marketData = TestDataBuilder.CreateBasicMarketData("AAPL");
var enhancedData = TestDataBuilder.CreateEnhancedMarketData("TSLA");
var newsItems = TestDataBuilder.CreateNewsItems("GOOGL", 5);
```

### MockFactory
Provides pre-configured mocks for dependencies:
```csharp
var mockDataService = MockFactory.CreateMockDataAggregationService();
var mockOpenAI = MockFactory.CreateMockOpenAIClient();
var mockProvider = MockFactory.CreateMockMarketDataProvider();
```

## 🔧 Configuration

### Test Settings
- Test-specific configuration in `TestData/appsettings.Test.json`
- Mock API keys and endpoints for testing
- Reduced timeouts and cache durations for faster tests

### Coverage Settings
- Configured in `coverlet.runsettings`
- Excludes test files and generated code
- Includes all source code for coverage analysis

## 📈 Test Metrics

### Current Coverage Goals
- **Line Coverage**: > 90%
- **Branch Coverage**: > 85%
- **Method Coverage**: > 95%

### Test Performance
- Unit tests should complete in < 5 seconds
- Integration tests should complete in < 30 seconds
- Full test suite should complete in < 60 seconds

## 🐛 Debugging Tests

### Visual Studio
1. Open the solution in Visual Studio
2. Use Test Explorer to run and debug individual tests
3. Set breakpoints in test methods or source code

### VS Code
1. Install C# extension
2. Use the Test Explorer extension
3. Run tests with debugging enabled

### Command Line
```bash
# Run tests with debugging
dotnet test --logger "console;verbosity=detailed"

# Run specific test with debugging
dotnet test --filter "TestMethodName" --logger "console;verbosity=detailed"
```

## 📝 Writing New Tests

### Test Naming Convention
- `MethodName_Scenario_ExpectedResult`
- Example: `GetMarketDataAsync_WithValidSymbol_ShouldReturnSuccessResponse`

### Test Structure
```csharp
[Fact]
public async Task MethodName_Scenario_ExpectedResult()
{
    // Arrange
    var input = TestDataBuilder.CreateTestData();
    var mock = MockFactory.CreateMock();
    
    // Act
    var result = await systemUnderTest.Method(input);
    
    // Assert
    result.Should().NotBeNull();
    result.Property.Should().Be(expectedValue);
}
```

### Best Practices
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Use FluentAssertions for readable assertions
- Mock external dependencies
- Test both success and failure scenarios
- Include edge cases and boundary conditions

## 🔄 Continuous Integration

The test suite is designed to run in CI/CD pipelines:
- All tests are self-contained
- No external dependencies required
- Deterministic test results
- Parallel execution support
- Coverage reporting integration

## 📞 Support

For questions about the test suite:
1. Check existing test examples
2. Review test utilities and helpers
3. Consult the main project documentation
4. Follow established testing patterns
