// Core/AgentEngine.cs
using Core;
using TradingAgent.Models;
using TradingAgent.Agents;

namespace TradingAgent.Core
{
    public interface IAgentEngine
    {
        Task<object> RunAsync(MarketData data);
    }

    public class AgentEngine : IAgentEngine
    {
        private readonly List<IAgent> _agents = new();
        private readonly Services.IOpenAIClient _openAIClient;
        private readonly ILogger<AgentEngine> _logger;

        public AgentEngine(Services.IOpenAIClient openAI, ILogger<AgentEngine> logger)
        {
            _openAIClient = openAI;
            _logger = logger;

            // AI-Powered Agents
            _agents.Add(new FundamentalAnalysisAgent(openAI));
            _agents.Add(new SentimentAnalysisAgent(openAI));
            _agents.Add(new NewsAnalysisAgent(openAI));
            _agents.Add(new RiskAnalysisAgent(openAI));

            // Technical Analysis Agents
            _agents.Add(new MomentumAnalysisAgent());
            _agents.Add(new SupportResistanceAgent());

            _logger.LogInformation("Initialized AgentEngine with {AgentCount} agents ({AIAgents} AI-powered, {TechnicalAgents} technical)",
                _agents.Count, 4, 2);
        }

        public async Task<object> RunAsync(MarketData data)
        {
            _logger.LogInformation("Starting analysis for symbol: {Symbol}", data.Symbol);

            var startTime = DateTime.UtcNow;
            var messages = await Task.WhenAll(_agents.Select(a => a.AnalyzeAsync(data)));
            var processingTime = DateTime.UtcNow - startTime;

            // Enhanced bias calculation
            var bias = DetermineBias(messages);
            var confidence = CalculateOverallConfidence(messages);

            // Get model information for AI-powered agents
            var agentDetails = messages.Select(m => new
            {
                agent = m.Sender,
                m.Insight,
                m.Confidence,
                model = GetAgentModel(m.Sender ?? "Unknown")
            }).ToArray();

            _logger.LogInformation("Analysis completed in {ProcessingTime}ms. Bias: {Bias}, Confidence: {Confidence:F2}",
                processingTime.TotalMilliseconds, bias, confidence);

            return new
            {
                symbol = data.Symbol,
                bias,
                confidence = Math.Round(confidence, 3),
                processingTimeMs = Math.Round(processingTime.TotalMilliseconds, 2),
                timestamp = DateTime.UtcNow,
                details = agentDetails
            };
        }

        private static string DetermineBias(AgentMessage[] messages)
        {
            // Weight by confidence and look for explicit recommendations
            var weightedVotes = new Dictionary<string, double>
            {
                ["BUY"] = 0,
                ["SELL"] = 0,
                ["HOLD"] = 0
            };

            foreach (var message in messages)
            {
                var insight = (message.Insight ?? "HOLD").ToUpper();
                var weight = message.Confidence;

                if (insight.Contains("BUY") || insight.Contains("BULLISH"))
                    weightedVotes["BUY"] += weight;
                else if (insight.Contains("SELL") || insight.Contains("BEARISH"))
                    weightedVotes["SELL"] += weight;
                else
                    weightedVotes["HOLD"] += weight;
            }

            return weightedVotes.OrderByDescending(kv => kv.Value).First().Key;
        }

        private static double CalculateOverallConfidence(AgentMessage[] messages)
        {
            // Weight confidence by the consistency of recommendations
            var averageConfidence = messages.Average(m => m.Confidence);
            var maxConfidence = messages.Max(m => m.Confidence);

            // Reduce confidence if agents disagree significantly
            var confidenceSpread = messages.Max(m => m.Confidence) - messages.Min(m => m.Confidence);
            var consistencyFactor = Math.Max(0.5, 1.0 - confidenceSpread);

            return Math.Min(averageConfidence * consistencyFactor, maxConfidence);
        }

        private string? GetAgentModel(string agentName)
        {
            // Return model info for AI-powered agents
            return agentName switch
            {
                "FundamentalAnalyst" => _openAIClient.GetModelForAgent(agentName),
                "SentimentAnalyst" => _openAIClient.GetModelForAgent(agentName),
                "NewsAnalyst" => _openAIClient.GetModelForAgent(agentName),
                "RiskAnalyst" => _openAIClient.GetModelForAgent(agentName),
                "MomentumAnalyst" or "SupportResistanceAnalyst" => "Technical Analysis", // Non-AI agents
                _ => null
            };
        }
    }
}