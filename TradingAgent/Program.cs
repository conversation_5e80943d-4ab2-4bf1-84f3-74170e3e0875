// Program.cs
using Config;
using DotNetEnv;
using TradingAgent.Core;
using TradingAgent.Services;

// Load environment variables from .env file
Env.Load();

var builder = WebApplication.CreateBuilder(args);

// Configure services
builder.Services.AddControllers();
// Temporarily disabled OpenAPI/Swagger for .NET 10 compatibility testing
// builder.Services.AddEndpointsApiExplorer();
// builder.Services.AddSwaggerGen();

// Add memory cache for data providers
builder.Services.AddMemoryCache();

// Configure settings with environment variable overrides
builder.Services.Configure<OpenAISettings>(options =>
{
    builder.Configuration.GetSection("OpenAI").Bind(options);

    // Override with environment variables if present
    var envApiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY");
    if (!string.IsNullOrEmpty(envApiKey))
    {
        options.ApiKey = envApiKey;
    }
});

builder.Services.Configure<DataProviderSettings>(options =>
{
    builder.Configuration.GetSection("DataProviders").Bind(options);

    // Override with environment variables if present
    var alphaVantageKey = Environment.GetEnvironmentVariable("ALPHAVANTAGE_API_KEY");
    if (!string.IsNullOrEmpty(alphaVantageKey))
    {
        options.AlphaVantage.ApiKey = alphaVantageKey;
    }

    var finnhubKey = Environment.GetEnvironmentVariable("FINNHUB_API_KEY");
    if (!string.IsNullOrEmpty(finnhubKey))
    {
        options.Finnhub.ApiKey = finnhubKey;
    }

    var polygonKey = Environment.GetEnvironmentVariable("POLYGON_API_KEY");
    if (!string.IsNullOrEmpty(polygonKey))
    {
        options.Polygon.ApiKey = polygonKey;
    }

    var polygonKeySecondary = Environment.GetEnvironmentVariable("POLYGON_API_KEY_SECONDARY");
    if (!string.IsNullOrEmpty(polygonKeySecondary))
    {
        options.Polygon.ApiKeySecondary = polygonKeySecondary;
    }

    var newsApiKey = Environment.GetEnvironmentVariable("NEWSAPI_API_KEY");
    if (!string.IsNullOrEmpty(newsApiKey))
    {
        options.NewsApi.ApiKey = newsApiKey;
    }

    var fredKey = Environment.GetEnvironmentVariable("FRED_API_KEY");
    if (!string.IsNullOrEmpty(fredKey))
    {
        options.Fred.ApiKey = fredKey;
    }

    // Override other settings if environment variables are present
    var primaryMarketProvider = Environment.GetEnvironmentVariable("PRIMARY_MARKET_DATA_PROVIDER");
    if (!string.IsNullOrEmpty(primaryMarketProvider))
    {
        options.PrimaryMarketDataProvider = primaryMarketProvider;
    }

    var primaryNewsProvider = Environment.GetEnvironmentVariable("PRIMARY_NEWS_PROVIDER");
    if (!string.IsNullOrEmpty(primaryNewsProvider))
    {
        options.PrimaryNewsProvider = primaryNewsProvider;
    }


});

// Register HTTP clients for data providers
builder.Services.AddHttpClient<AlphaVantageProvider>();
builder.Services.AddHttpClient<YahooFinanceProvider>();
builder.Services.AddHttpClient<FinnhubProvider>();
builder.Services.AddHttpClient<PolygonProvider>();
builder.Services.AddHttpClient<NewsApiProvider>();
builder.Services.AddHttpClient<FredEconomicProvider>();

// Register data providers
builder.Services.AddScoped<IMarketDataProvider, AlphaVantageProvider>();
builder.Services.AddScoped<IMarketDataProvider, YahooFinanceProvider>();
builder.Services.AddScoped<IMarketDataProvider, FinnhubProvider>();
builder.Services.AddScoped<IMarketDataProvider, PolygonProvider>();
builder.Services.AddScoped<INewsProvider, AlphaVantageProvider>();
builder.Services.AddScoped<INewsProvider, FinnhubProvider>();
builder.Services.AddScoped<INewsProvider, PolygonProvider>();
builder.Services.AddScoped<INewsProvider, NewsApiProvider>();
builder.Services.AddScoped<IEconomicDataProvider, FredEconomicProvider>();

// Register aggregation service
builder.Services.AddScoped<IDataAggregationService, DataAggregationService>();

// Register existing services
builder.Services.AddSingleton<IOpenAIClient, OpenAIClient>();
builder.Services.AddSingleton<IAgentEngine, AgentEngine>();

// Add logging
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    // Temporarily disabled Swagger for .NET 10 compatibility testing
    // app.UseSwagger();
    // app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

app.Run();

// Make Program class accessible for testing
public partial class Program { }