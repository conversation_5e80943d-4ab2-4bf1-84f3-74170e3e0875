#!/bin/bash

# TradingAgent Environment Setup Script
# This script helps you set up your .env file with API keys

echo "🚀 TradingAgent Environment Setup"
echo "=================================="
echo ""

# Check if .env file exists
if [ -f ".env" ]; then
    echo "⚠️  .env file already exists!"
    read -p "Do you want to overwrite it? (y/N): " overwrite
    if [[ ! $overwrite =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
fi

# Copy from template
if [ -f ".env.example" ]; then
    cp .env.example .env
    echo "✅ Created .env file from template"
else
    echo "❌ .env.example file not found!"
    exit 1
fi

echo ""
echo "📝 Let's set up your API keys..."
echo "   (Press Enter to skip any provider)"
echo ""

# Function to update .env file
update_env() {
    local key=$1
    local value=$2
    if [ ! -z "$value" ]; then
        if grep -q "^${key}=" .env; then
            sed -i "s|^${key}=.*|${key}=${value}|" .env
        else
            echo "${key}=${value}" >> .env
        fi
        echo "✅ Set ${key}"
    else
        echo "⏭️  Skipped ${key}"
    fi
}

# Collect API keys
echo "🔑 Alpha Vantage API Key (https://www.alphavantage.co/support/#api-key):"
read -p "   Enter key: " alphavantage_key
update_env "ALPHAVANTAGE_API_KEY" "$alphavantage_key"

echo ""
echo "🔑 Finnhub API Key (https://finnhub.io/register):"
read -p "   Enter key: " finnhub_key
update_env "FINNHUB_API_KEY" "$finnhub_key"

echo ""
echo "🔑 Polygon.io API Key (https://polygon.io/):"
read -p "   Enter key: " polygon_key
update_env "POLYGON_API_KEY" "$polygon_key"

echo ""
echo "🔑 NewsAPI Key (https://newsapi.org/):"
read -p "   Enter key: " newsapi_key
update_env "NEWSAPI_API_KEY" "$newsapi_key"

echo ""
echo "🔑 FRED API Key (https://fred.stlouisfed.org/docs/api/api_key.html):"
read -p "   Enter key: " fred_key
update_env "FRED_API_KEY" "$fred_key"

echo ""
echo "🔑 OpenAI API Key (https://platform.openai.com/api-keys):"
read -p "   Enter key: " openai_key
update_env "OPENAI_API_KEY" "$openai_key"

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Summary:"
echo "   - .env file created/updated"
echo "   - API keys configured"
echo "   - Ready to run: dotnet run"
echo ""
echo "💡 Tips:"
echo "   - Yahoo Finance works without API keys (free fallback)"
echo "   - FRED provides free economic data (highly recommended)"
echo "   - Start with free tiers to test functionality"
echo "   - Never commit .env file to version control"
echo ""
echo "🚀 Start the server with: dotnet run"
