// Models/MarketData.cs
namespace TradingAgent.Models
{
    public class MarketData
    {
        public string Symbol { get; set; } = string.Empty;
        public double Price { get; set; }
        public double PERatio { get; set; }
        public string Sector { get; set; } = string.Empty;
        public string RawNews { get; set; } = string.Empty;
        public double RSI { get; set; }
        public double MACD { get; set; }
        public List<double> RecentPrices { get; set; } = new();
    }
}