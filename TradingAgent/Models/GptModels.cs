namespace TradingAgent.Models
{
    public static class GptModels
    {
        public static readonly string GPT_35_TURBO = "gpt-3.5-turbo";
        public static readonly string GPT_35_TURBO_16K = "gpt-3.5-turbo-16k";
        public static readonly string GPT_4 = "gpt-4";
        public static readonly string GPT_4_32K = "gpt-4-32k";
        public static readonly string GPT_4o = "gpt-4o";
        public static readonly string GPT_4o_MINI = "gpt-4o-mini";

        // Optional local model aliases
        public static readonly string Mistral = "mistral";
        public static readonly string Mixtral = "mixtral";
        public static readonly string DeepSeek = "deepseek-coder";
        public static readonly string Claude = "claude-3-sonnet";

        /// <summary>
        /// Default model if not explicitly set
        /// </summary>
        public static string Default => GPT_4o;

        /// <summary>
        /// Get all available model keys
        /// </summary>
        public static IEnumerable<string> All =>
            new[]
            {
                GPT_35_TURBO,
                GPT_35_TURBO_16K,
                GPT_4,
                GPT_4_32K,
                GPT_4o,
                GPT_4o_MINI,
                Mistral,
                Mixtral,
                DeepSeek,
                Claude
            };

        /// <summary>
        /// Get OpenAI-specific models only
        /// </summary>
        public static IEnumerable<string> OpenAIModels =>
            new[]
            {
                GPT_35_TURBO,
                GPT_35_TURBO_16K,
                GPT_4,
                GPT_4_32K,
                GPT_4o,
                GPT_4o_MINI
            };

        /// <summary>
        /// Get local/alternative models
        /// </summary>
        public static IEnumerable<string> LocalModels =>
            new[]
            {
                Mistral,
                Mixtral,
                DeepSeek,
                Claude
            };

        /// <summary>
        /// Check if a model is supported
        /// </summary>
        public static bool IsSupported(string model) => All.Contains(model);

        /// <summary>
        /// Get model information including cost tier and capabilities
        /// </summary>
        public static ModelInfo GetModelInfo(string model)
        {
            return model switch
            {
                "gpt-3.5-turbo" => new ModelInfo("GPT-3.5 Turbo", "Low", "Fast, cost-effective for simple tasks"),
                "gpt-3.5-turbo-16k" => new ModelInfo("GPT-3.5 Turbo 16K", "Low", "Extended context for complex analysis"),
                "gpt-4" => new ModelInfo("GPT-4", "High", "Advanced reasoning and analysis"),
                "gpt-4-32k" => new ModelInfo("GPT-4 32K", "High", "Extended context for comprehensive analysis"),
                "gpt-4o" => new ModelInfo("GPT-4o", "Medium", "Optimized for performance and cost balance"),
                "gpt-4o-mini" => new ModelInfo("GPT-4o Mini", "Low", "Lightweight version for quick analysis"),
                "mistral" => new ModelInfo("Mistral", "Local", "Open source alternative"),
                "mixtral" => new ModelInfo("Mixtral", "Local", "Mixture of experts model"),
                "deepseek-coder" => new ModelInfo("DeepSeek Coder", "Local", "Specialized for code and technical analysis"),
                "claude-3-sonnet" => new ModelInfo("Claude 3 Sonnet", "Medium", "Anthropic's balanced model"),
                _ => new ModelInfo("Unknown", "Unknown", "Model information not available")
            };
        }
    }

    public record ModelInfo(string DisplayName, string CostTier, string Description);
}