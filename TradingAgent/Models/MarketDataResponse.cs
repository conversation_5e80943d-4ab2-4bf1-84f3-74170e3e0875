// Models/MarketDataResponse.cs
namespace TradingAgent.Models
{
    /// <summary>
    /// Enhanced market data with source tracking and metadata
    /// </summary>
    public class EnhancedMarketData : MarketData
    {
        public string DataSource { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; }
        public bool IsRealTime { get; set; }
        public double? DayChange { get; set; }
        public double? DayChangePercent { get; set; }
        public double? Volume { get; set; }
        public double? MarketCap { get; set; }
        public double? DividendYield { get; set; }
        public double? EPS { get; set; } // Earnings Per Share
        public double? ROE { get; set; } // Return on Equity
        public string? CompanyName { get; set; }
        public string? Industry { get; set; }
        public string? Country { get; set; }
        public List<NewsItem> RelatedNews { get; set; } = new();
        public EconomicIndicators? EconomicData { get; set; }
    }

    public class NewsItem
    {
        public string? Title { get; set; }
        public string? Summary { get; set; }
        public string? Url { get; set; }
        public DateTime PublishedAt { get; set; }
        public string Source { get; set; } = string.Empty;
        public double SentimentScore { get; set; } // -1 to 1, where 1 is most positive
        public List<string> Keywords { get; set; } = new();
    }

    public class EconomicIndicators
    {
        public double? FedFundsRate { get; set; }
        public double? InflationRate { get; set; }
        public double? UnemploymentRate { get; set; }
        public double? GDP_Growth { get; set; }
        public double? VIX { get; set; } // Market volatility index
        public double? DXY { get; set; } // Dollar index
        public double? CrudeOilPrice { get; set; }
        public double? GoldPrice { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Request model for fetching market data
    /// </summary>
    public class MarketDataRequest
    {
        public string Symbol { get; set; } = string.Empty;
        public bool IncludeNews { get; set; } = true;
        public bool IncludeEconomicData { get; set; } = true;
        public bool ForceRefresh { get; set; } = false;
        public int NewsLookbackDays { get; set; } = 7;
        public string? PreferredDataSource { get; set; }
    }

    /// <summary>
    /// Response wrapper with metadata
    /// </summary>
    public class DataProviderResponse<T>
    {
        public T? Data { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string DataSource { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool FromCache { get; set; }
        public TimeSpan ResponseTime { get; set; }
    }
}