// Agents/FundamentalAnalysisAgent.cs
using TradingAgent.Services;
using TradingAgent.Models;
using Core;

namespace TradingAgent.Agents
{
    public class FundamentalAnalysisAgent(IOpenAIClient client) : IAgent
{
    private readonly IOpenAIClient _openAI = client;
    public string Name => "FundamentalAnalyst";

    public async Task<AgentMessage> AnalyzeAsync(MarketData data)
    {
        var enhancedData = data as EnhancedMarketData;
        var model = _openAI.GetModelForAgent(Name);

        string systemPrompt = @"You are a professional fundamental analyst. Analyze the provided financial data and provide:
1. A clear BUY/SELL/HOLD recommendation
2. Key reasoning based on fundamentals
3. Risk factors to consider
Format your response clearly and concisely.";

        string userPrompt = $@"Analyze the fundamental aspects of {data.Symbol}:
- Current Price: ${data.Price:F2}
- PE Ratio: {data.PERatio:F2}
- Sector: {data.Sector}
- Recent News: {data.RawNews}";

        // Add enhanced metrics if available
        if (enhancedData != null)
        {
            userPrompt += $@"
- Market Cap: ${enhancedData.MarketCap:N0}
- EPS: ${enhancedData.EPS:F2}
- ROE: {enhancedData.ROE:F2}%";
        }

        userPrompt += "\n\nProvide a clear trading recommendation with reasoning.";

        var result = await _openAI.AskAsync(userPrompt, model, systemPrompt);
        
        // Calculate confidence based on available data quality
        double confidence = CalculateConfidence(data);
        
        return new AgentMessage { Sender = Name, Insight = result, Confidence = confidence };
    }

    private static double CalculateConfidence(MarketData data)
    {
        double confidence = 0.5; // Base confidence
        
        // Increase confidence based on data quality
        if (data.PERatio > 0 && data.PERatio < 100) confidence += 0.2;
        if (!string.IsNullOrEmpty(data.Sector)) confidence += 0.1;
        if (!string.IsNullOrEmpty(data.RawNews)) confidence += 0.2;
        
        return Math.Min(confidence, 1.0);
    }
}
}