// Agents/NewsAnalyst.cs
using Core;
using TradingAgent.Models;
using TradingAgent.Services;

namespace TradingAgent.Agents
{
    public class NewsAnalysisAgent(IOpenAIClient client) : IAgent
{
    private readonly IOpenAIClient _openAI = client;
    public string Name => "NewsAnalyst";

    public async Task<AgentMessage> AnalyzeAsync(MarketData data)
    {
        var enhancedData = data as EnhancedMarketData;
        var model = _openAI.GetModelForAgent(Name);
        
        string systemPrompt = @"You are a financial news analyst expert at interpreting news events and their market impact.
                                Analyze recent news and determine its potential impact on stock price and trading volumes.
                                Focus on:
                                1. Earnings reports and financial performance
                                2. Product launches and business developments  
                                3. Regulatory changes and legal issues
                                4. Market conditions and industry trends
                                5. Management changes and strategic decisions

                                Provide a clear BUY/SELL/HOLD recommendation based on news analysis.";

        string newsAnalysis = "";
        if (enhancedData?.RelatedNews?.Any() == true)
        {
            var sortedNews = enhancedData.RelatedNews
                .OrderByDescending(n => n.PublishedAt)
                .Take(10);

            newsAnalysis = string.Join("\n", sortedNews.Select(n => 
                $"[{n.PublishedAt:MM/dd HH:mm}] {n.Title}\n  Summary: {n.Summary}\n  Keywords: {string.Join(", ", n.Keywords)}\n"));
        }

        string userPrompt = $@"Analyze recent news impact for {data.Symbol}:

                Company: {enhancedData?.CompanyName ?? data.Symbol}
                Sector: {data.Sector}
                Current Price: ${data.Price:F2}

                Recent News Analysis:
                {newsAnalysis}

                Additional Context: {data.RawNews}

                Analyze the news impact and provide trading recommendation with specific reasoning.";

        var result = await _openAI.AskAsync(userPrompt, model, systemPrompt);
        
        // Calculate confidence based on news quality and recency
        double confidence = CalculateConfidence(enhancedData);
        
        return new AgentMessage { Sender = Name, Insight = result, Confidence = confidence };
    }

    private static double CalculateConfidence(EnhancedMarketData? data)
    {
        double confidence = 0.3; // Base confidence
        
        if (data?.RelatedNews?.Any() == true)
        {
            confidence += 0.3; // Boost for having news
            
            var recentNews = data.RelatedNews.Where(n => n.PublishedAt > DateTime.UtcNow.AddDays(-2)).ToList();
            if (recentNews.Any())
            {
                confidence += 0.2; // Boost for recent news
                
                // Check for high-impact keywords
                var impactKeywords = new[] { "earnings", "revenue", "profit", "merger", "acquisition", "ipo", "dividend" };
                var hasImpactNews = recentNews.Any(n =>
                    n.Keywords.Any(k => impactKeywords.Contains(k, StringComparer.OrdinalIgnoreCase)) ||
                    impactKeywords.Any(ik => (n.Title ?? "").Contains(ik, StringComparison.OrdinalIgnoreCase))
                );
                
                if (hasImpactNews) confidence += 0.2;
            }
        }
        
        return Math.Min(confidence, 1.0);
    }
}
}