// Agents/RiskAnalyst.cs
using Core;
using TradingAgent.Models;
using TradingAgent.Services;

namespace TradingAgent.Agents
{
    public class RiskAnalysisAgent(IOpenAIClient client) : IAgent
{
    private readonly IOpenAIClient _openAI = client;
    public string Name => "RiskAnalyst";

    public async Task<AgentMessage> AnalyzeAsync(MarketData data)
    {
        var enhancedData = data as EnhancedMarketData;
        var model = _openAI.GetModelForAgent(Name);
        
        string systemPrompt = @"You are a risk management specialist focused on identifying and quantifying investment risks.
Analyze the provided financial and economic data to assess risk factors including:
1. Market volatility and technical risk indicators
2. Economic environment and systemic risks  
3. Company-specific risks from news and fundamentals
4. Sector and industry risks
5. Liquidity and volume risks

Provide a risk assessment and position sizing recommendation (BUY/SELL/HOLD with position size).";

        string economicContext = "";
        if (enhancedData?.EconomicData != null)
        {
            var econ = enhancedData.EconomicData;
            economicContext = $@"
Economic Environment:
- Fed Funds Rate: {econ.FedFundsRate:F2}%
- Inflation Rate: {econ.InflationRate:F2}%
- Unemployment: {econ.UnemploymentRate:F2}%
- VIX (Volatility): {econ.VIX:F2}
- Crude Oil: ${econ.CrudeOilPrice:F2}
- Gold: ${econ.GoldPrice:F2}";
        }

        string riskFactors = AnalyzeRiskFactors(enhancedData);

        string userPrompt = $@"Risk Analysis for {data.Symbol}:

Current Price: ${data.Price:F2}
PE Ratio: {data.PERatio:F2}
Sector: {data.Sector}
RSI: {data.RSI:F2}
MACD: {data.MACD:F2}
Day Change: {enhancedData?.DayChangePercent:F2}%
Volume: {enhancedData?.Volume:N0}

{economicContext}

Risk Factors Identified:
{riskFactors}

Recent News Sentiment: {enhancedData?.RelatedNews?.Average(n => n.SentimentScore):F2}

Provide comprehensive risk assessment and trading recommendation with position sizing guidance.";

        var result = await _openAI.AskAsync(userPrompt, model, systemPrompt);
        
        // Calculate confidence based on data completeness and risk clarity
        double confidence = CalculateConfidence(enhancedData);
        
        return new AgentMessage { Sender = Name, Insight = result, Confidence = confidence };
    }

    private static string AnalyzeRiskFactors(EnhancedMarketData? data)
    {
        var riskFactors = new List<string>();
        
        if (data == null) return "Limited data available for risk analysis";

        // Technical risk factors
        if (data.RSI > 70)
            riskFactors.Add("⚠️ Overbought condition (RSI > 70) - Price reversal risk");
        else if (data.RSI < 30)
            riskFactors.Add("📈 Oversold condition (RSI < 30) - Potential bounce opportunity");

        // Volatility risk
        if (data.EconomicData?.VIX > 30)
            riskFactors.Add("⚠️ High market volatility (VIX > 30) - Increased systemic risk");

        // Fundamental risk factors
        if (data.PERatio > 40)
            riskFactors.Add("⚠️ High valuation (PE > 40) - Overvaluation risk");
        else if (data.PERatio < 10 && data.PERatio > 0)
            riskFactors.Add("📊 Low valuation (PE < 10) - Potential value opportunity or distress");

        // Economic risk factors
        if (data.EconomicData?.FedFundsRate > 5)
            riskFactors.Add("⚠️ High interest rates - Increased borrowing costs");
        
        if (data.EconomicData?.InflationRate > 5)
            riskFactors.Add("⚠️ High inflation - Purchasing power and margin pressure");

        // News-based risk factors
        if (data.RelatedNews?.Any() == true)
        {
            var avgSentiment = data.RelatedNews.Average(n => n.SentimentScore);
            if (avgSentiment < -0.3)
                riskFactors.Add("⚠️ Negative news sentiment - Reputation and confidence risk");
            
            var hasLegalNews = data.RelatedNews.Any(n => 
                n.Keywords.Any(k => new[] { "lawsuit", "investigation", "regulatory", "fine" }
                    .Contains(k, StringComparer.OrdinalIgnoreCase)));
            if (hasLegalNews)
                riskFactors.Add("⚠️ Legal/regulatory issues - Compliance and penalty risk");
        }

        // Volume/liquidity risk
        if (data.Volume < 100000)
            riskFactors.Add("⚠️ Low trading volume - Liquidity risk");

        return riskFactors.Any() ? string.Join("\n", riskFactors) : "✅ No significant risk factors identified";
    }

    private static double CalculateConfidence(EnhancedMarketData? data)
    {
        double confidence = 0.5; // Base confidence for risk analysis
        
        if (data != null)
        {
            // Boost confidence based on data availability
            if (data.EconomicData != null) confidence += 0.2;
            if (data.RelatedNews?.Any() == true) confidence += 0.15;
            if (data.Volume > 0) confidence += 0.1;
            if (data.RSI > 0 && data.RSI <= 100) confidence += 0.05;
        }
        
        return Math.Min(confidence, 1.0);
    }
}
}