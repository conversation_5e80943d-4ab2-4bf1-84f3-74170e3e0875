// Agents/MomentumAnalysisAgent.cs
using TradingAgent.Models;
using Core;

namespace TradingAgent.Agents
{
    public class MomentumAnalysisAgent : IAgent
    {
        public string Name => "MomentumAnalyst";

        public Task<AgentMessage> AnalyzeAsync(MarketData data)
        {
            var insight = data.RSI >= 70 ? "Overbought" : data.RSI <= 30 ? "Oversold" : "Neutral";
            double confidence = (insight == "Neutral") ? 0.5 : 0.9;
            return Task.FromResult(new AgentMessage { Sender = Name, Insight = insight, Confidence = confidence });
        }
    }
}