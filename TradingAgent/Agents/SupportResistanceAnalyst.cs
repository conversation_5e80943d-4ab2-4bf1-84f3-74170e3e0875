// Agents/SupportResistanceAgent.cs
using TradingAgent.Models;
using Core;

namespace TradingAgent.Agents
{
    public class SupportResistanceAgent : IAgent
    {
        public string Name => "SupportResistanceAnalyst";

        public Task<AgentMessage> AnalyzeAsync(MarketData data)
        {
            var recentHigh = data.RecentPrices.Max();
            var recentLow = data.RecentPrices.Min();
            var range = recentHigh - recentLow;
            var nearResistance = data.Price >= recentHigh - (0.1 * range);

            var insight = nearResistance ? "Near Resistance" : "Safe Zone";
            return Task.FromResult(new AgentMessage { Sender = Name, Insight = insight, Confidence = nearResistance ? 0.8 : 0.6 });
        }
    }
}