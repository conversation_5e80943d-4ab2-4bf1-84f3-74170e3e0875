// Agents/SentimentAnalyst.cs
using Core;
using TradingAgent.Models;
using TradingAgent.Services;

namespace TradingAgent.Agents
{
    public class SentimentAnalysisAgent(IOpenAIClient client) : IAgent
{
    private readonly IOpenAIClient _openAI = client;
    public string Name => "SentimentAnalyst";

    public async Task<AgentMessage> AnalyzeAsync(MarketData data)
    {
        var enhancedData = data as EnhancedMarketData;
        var model = _openAI.GetModelForAgent(Name);
        
        string systemPrompt = @"You are a sentiment analysis expert specializing in financial news and market sentiment. 
Analyze the provided news and market sentiment data to determine its impact on the stock price.
Provide a clear BUY/SELL/HOLD recommendation based on sentiment analysis.
Consider both news sentiment and social media sentiment if available.";

        string newsContext = "";
        if (enhancedData?.RelatedNews?.Count > 0 == true)
        {
            var recentNews = enhancedData.RelatedNews.Take(5);
            newsContext = string.Join("\n", recentNews.Select(n => 
                $"- {n.Title} (Sentiment: {n.SentimentScore:F2}, Source: {n.Source})"));
        }

        string userPrompt = $@"Analyze sentiment for {data.Symbol}:

Recent News ({enhancedData?.RelatedNews?.Count ?? 0} articles):
{newsContext}

Current Price: ${data.Price:F2}
Sector: {data.Sector}
Raw News Summary: {data.RawNews}

Provide sentiment analysis and trading recommendation with confidence level.";

        var result = await _openAI.AskAsync(userPrompt, model, systemPrompt);
        
        // Calculate confidence based on news availability and sentiment consistency
        double confidence = CalculateConfidence(enhancedData);
        
        return new AgentMessage { Sender = Name, Insight = result, Confidence = confidence };
    }

    private static double CalculateConfidence(EnhancedMarketData? data)
    {
        double confidence = 0.4; // Base confidence for sentiment analysis
        
        if (data?.RelatedNews?.Any() == true)
        {
            confidence += 0.3; // Boost if we have news data
            
            // Check sentiment consistency
            var sentiments = data.RelatedNews.Select(n => n.SentimentScore).ToList();
            if (sentiments.Count != 0)
            {
                var avgSentiment = sentiments.Average();
                var sentimentStdDev = CalculateStandardDeviation(sentiments);
                
                // Higher confidence if sentiment is consistent
                if (sentimentStdDev < 0.5) confidence += 0.2;
                
                // Higher confidence for strong sentiment (positive or negative)
                if (Math.Abs(avgSentiment) > 0.3) confidence += 0.1;
            }
        }
        
        return Math.Min(confidence, 1.0);
    }

    private static double CalculateStandardDeviation(List<double> values)
    {
        if (values.Count <= 1) return 0;
        
        var mean = values.Average();
        var sumOfSquares = values.Sum(x => Math.Pow(x - mean, 2));
        return Math.Sqrt(sumOfSquares / values.Count);
    }
}
}