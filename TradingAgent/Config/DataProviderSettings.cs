// Config/DataProviderSettings.cs
namespace Config
{
    public class DataProviderSettings
    {
        public AlphaVantageSettings AlphaVantage { get; set; } = new();
        public FinnhubSettings Finnhub { get; set; } = new();
        public PolygonSettings Polygon { get; set; } = new();
        public NewsApiSettings NewsApi { get; set; } = new();
        public FredSettings Fred { get; set; } = new();
        public YahooFinanceSettings YahooFinance { get; set; } = new();
        
        /// <summary>
        /// Primary data provider for market data
        /// </summary>
        public string PrimaryMarketDataProvider { get; set; } = "AlphaVantage";
        
        /// <summary>
        /// Primary data provider for news
        /// </summary>
        public string PrimaryNewsProvider { get; set; } = "NewsApi";
        
        /// <summary>
        /// Cache duration for market data in minutes
        /// </summary>
        public int MarketDataCacheMinutes { get; set; } = 5;
        
        /// <summary>
        /// Cache duration for news data in minutes
        /// </summary>
        public int NewsCacheMinutes { get; set; } = 15;
        
        /// <summary>
        /// Enable fallback to secondary providers
        /// </summary>
        public bool EnableFallback { get; set; } = true;
    }

    public class AlphaVantageSettings
    {
        public string ApiKey { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = "https://www.alphavantage.co/query";
        public int RateLimitPerMinute { get; set; } = 75; // Free tier: 75 requests/minute
        public int TimeoutSeconds { get; set; } = 30;
    }

    public class FinnhubSettings
    {
        public string ApiKey { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = "https://finnhub.io/api/v1";
        public int RateLimitPerMinute { get; set; } = 60; // Free tier: 60 requests/minute
        public int RateLimitPerSecond { get; set; } = 30; // Free tier: 30 requests/second
        public int TimeoutSeconds { get; set; } = 30;
    }

    public class PolygonSettings
    {
        public string ApiKey { get; set; } = string.Empty;
        public string ApiKeySecondary { get; set; } = string.Empty; // Secondary key for fallback
        public string BaseUrl { get; set; } = "https://api.polygon.io";
        public int RateLimitPerMinute { get; set; } = 5; // Free tier: 5 requests/minute
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Use primary key for news, secondary for market data
        /// </summary>
        public bool UseSecondaryForMarketData { get; set; } = true;
    }

    public class NewsApiSettings
    {
        public string ApiKey { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = "https://newsapi.org/v2";
        public int RateLimitPerDay { get; set; } = 500; // Free tier: 500 requests/day
        public int TimeoutSeconds { get; set; } = 30;
    }

    public class FredSettings
    {
        public string ApiKey { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = "https://api.stlouisfed.org/fred";
        public int TimeoutSeconds { get; set; } = 30;
    }

    public class YahooFinanceSettings
    {
        public string BaseUrl { get; set; } = "https://query1.finance.yahoo.com";
        public int RateLimitPerMinute { get; set; } = 2000; // Unofficial limit
        public int TimeoutSeconds { get; set; } = 30;
        public bool EnableRealTime { get; set; } = true;
    }
}