// Config/OpenAISettings.cs
using TradingAgent.Models;

namespace Config
{
    public class OpenAISettings
    {
        public string ApiKey { get; set; } = string.Empty;
        public string DefaultModel { get; set; } = GptModels.GPT_4o;
        public string BaseUrl { get; set; } = "https://api.openai.com/v1";
        public int MaxRetries { get; set; } = 3;
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Model-specific configurations for different agents
        /// </summary>
        public AgentModelSettings AgentModels { get; set; } = new();
    }

    public class AgentModelSettings
    {
        public string FundamentalAnalyst { get; set; } = GptModels.GPT_4o;
        public string SentimentAnalyst { get; set; } = GptModels.GPT_35_TURBO;
        public string NewsAnalyst { get; set; } = GptModels.GPT_4o;
        public string RiskAnalyst { get; set; } = GptModels.GPT_4;
    }
}