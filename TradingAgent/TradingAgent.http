@TradingAgent_HostAddress = http://localhost:5285

### Analyze a symbol with automatic data fetching (New Primary Method)
POST {{TradingAgent_HostAddress}}/analyze/symbol/AAPL
Content-Type: application/json

### Analyze a symbol with custom parameters
POST {{TradingAgent_HostAddress}}/analyze/symbol/TSLA?includeNews=true&includeEconomicData=true&newsLookbackDays=3&forceRefresh=true

### Analyze a symbol using preferred data source
POST {{TradingAgent_HostAddress}}/analyze/symbol/MSFT?preferredDataSource=YahooFinance

### Get available symbols for analysis
GET {{TradingAgent_HostAddress}}/analyze/symbols

### Get symbols by sector
GET {{TradingAgent_HostAddress}}/analyze/symbols?sector=technology

### Check data provider health status
GET {{TradingAgent_HostAddress}}/analyze/health

### Legacy: Send manual market data (still supported)
POST {{TradingAgent_HostAddress}}/analyze
Content-Type: application/json

{
  "symbol": "AAPL",
  "price": 150.0,
  "peRatio": 25.5,
  "sector": "Technology",
  "rawNews": "Apple reports strong quarterly earnings with record iPhone sales",
  "rsi": 65.0,
  "macd": 1.2,
  "recentPrices": [145.0, 148.0, 149.5, 150.0, 151.0]
}

### Model Management APIs

### Get available models
GET {{TradingAgent_HostAddress}}/models/available

### Get current model configuration
GET {{TradingAgent_HostAddress}}/models/configuration

### Test a specific model
POST {{TradingAgent_HostAddress}}/models/test
Content-Type: application/json

{
  "model": "gpt-3.5-turbo"
}

### Update agent model (demo)
PUT {{TradingAgent_HostAddress}}/models/agent/FundamentalAnalyst
Content-Type: application/json

{
  "model": "gpt-4o"
}

###
