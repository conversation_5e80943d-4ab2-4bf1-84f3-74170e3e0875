// Controllers/AgentController.cs
using Microsoft.AspNetCore.Mvc;
using TradingAgent.Models;
using Core;
using TradingAgent.Services;
using TradingAgent.Core;

namespace TradingAgent.Controllers
{
    [ApiController]
    [Route("/analyze")]
    public class AgentController : ControllerBase
{
    private readonly IAgentEngine _engine;
    private readonly IDataAggregationService _dataService;

    public AgentController(IAgentEngine engine, IDataAggregationService dataService)
    {
        _engine = engine;
        _dataService = dataService;
    }

    /// <summary>
    /// Analyze market data (supports both manual data input and automatic fetching)
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Analyze([FromBody] MarketData data)
    {
        var result = await _engine.RunAsync(data);
        return Ok(result);
    }

    /// <summary>
    /// Analyze a symbol by automatically fetching all required data
    /// </summary>
    [HttpPost("symbol/{symbol}")]
    public async Task<IActionResult> AnalyzeSymbol(
        string symbol, 
        [FromQuery] bool includeNews = true,
        [FromQuery] bool includeEconomicData = true,
        [FromQuery] int newsLookbackDays = 7,
        [FromQuery] string? preferredDataSource = null,
        [FromQuery] bool forceRefresh = false)
    {
        try
        {
            var request = new MarketDataRequest
            {
                Symbol = symbol.ToUpper(),
                IncludeNews = includeNews,
                IncludeEconomicData = includeEconomicData,
                NewsLookbackDays = newsLookbackDays,
                PreferredDataSource = preferredDataSource,
                ForceRefresh = forceRefresh
            };

            var dataResponse = await _dataService.GetCompleteMarketDataAsync(request);
            
            if (!dataResponse.Success || dataResponse.Data == null)
            {
                return BadRequest(new { 
                    error = "Failed to fetch market data", 
                    details = dataResponse.ErrorMessage 
                });
            }

            var analysisResult = await _engine.RunAsync(dataResponse.Data);
            
            return Ok(new
            {
                analysis = analysisResult,
                dataMetadata = new
                {
                    dataSource = dataResponse.DataSource,
                    fromCache = dataResponse.FromCache,
                    dataFetchTime = dataResponse.ResponseTime.TotalMilliseconds,
                    lastUpdated = dataResponse.Data.LastUpdated,
                    isRealTime = dataResponse.Data.IsRealTime,
                    newsCount = dataResponse.Data.RelatedNews.Count,
                    hasEconomicData = dataResponse.Data.EconomicData != null
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Get available symbols for analysis
    /// </summary>
    [HttpGet("symbols")]
    public async Task<IActionResult> GetAvailableSymbols([FromQuery] string? sector = null)
    {
        try
        {
            var symbols = await _dataService.GetAvailableSymbolsAsync(sector);
            return Ok(new { symbols, sector });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to fetch symbols", details = ex.Message });
        }
    }

    /// <summary>
    /// Get health status of all data providers
    /// </summary>
    [HttpGet("health")]
    public async Task<IActionResult> GetProviderHealth()
    {
        try
        {
            var healthStatus = await _dataService.GetProviderHealthStatusAsync();
            var overallHealth = healthStatus.Values.Any(h => h);

            return Ok(new {
                overallHealthy = overallHealth,
                providers = healthStatus,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to check provider health", details = ex.Message });
        }
    }

    /// <summary>
    /// Get trading signal in cTrader-compatible flat JSON format
    /// </summary>
    [HttpGet("ctrader/{symbol}")]
    public async Task<IActionResult> GetCTraderSignal(
        string symbol,
        [FromQuery] string format = "raw", // raw, summary, technical
        [FromQuery] string? preferredDataSource = null,
        [FromQuery] bool forceRefresh = false)
    {
        try
        {
            var request = new MarketDataRequest
            {
                Symbol = symbol.ToUpper(),
                IncludeNews = false, // Disable news to avoid API issues
                IncludeEconomicData = false, // Keep it simple for cTrader
                NewsLookbackDays = 0, // No news
                PreferredDataSource = preferredDataSource,
                ForceRefresh = forceRefresh
            };

            var dataResponse = await _dataService.GetCompleteMarketDataAsync(request);

            if (!dataResponse.Success || dataResponse.Data == null)
            {
                return BadRequest(new {
                    error = "Failed to fetch market data",
                    details = dataResponse.ErrorMessage
                });
            }

            // Try AI analysis, but continue without it if it fails
            object? analysisResult = null;
            try
            {
                analysisResult = await _engine.RunAsync(dataResponse.Data);
            }
            catch (Exception ex)
            {
                // Log the error but continue with mock analysis
                analysisResult = new
                {
                    bias = "HOLD",
                    confidence = 0.5,
                    details = new object[0]
                };
            }

            // Convert to cTrader-compatible format
            var ctraderResponse = format.ToLower() switch
            {
                "raw" => CreateRawDataResponse(dataResponse.Data, analysisResult),
                "summary" => CreateSummaryResponse(dataResponse.Data, analysisResult),
                "technical" => CreateTechnicalResponse(dataResponse.Data, analysisResult),
                _ => CreateRawDataResponse(dataResponse.Data, analysisResult)
            };

            return Ok(ctraderResponse);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Test endpoint to get raw market data without AI analysis
    /// </summary>
    [HttpGet("data/{symbol}")]
    public async Task<IActionResult> GetMarketData(
        string symbol,
        [FromQuery] bool includeNews = false,
        [FromQuery] bool includeEconomicData = false,
        [FromQuery] int newsLookbackDays = 7,
        [FromQuery] string? preferredDataSource = null,
        [FromQuery] bool forceRefresh = false)
    {
        try
        {
            var request = new MarketDataRequest
            {
                Symbol = symbol.ToUpper(),
                IncludeNews = includeNews,
                IncludeEconomicData = includeEconomicData,
                NewsLookbackDays = newsLookbackDays,
                PreferredDataSource = preferredDataSource,
                ForceRefresh = forceRefresh
            };

            var dataResponse = await _dataService.GetCompleteMarketDataAsync(request);

            if (!dataResponse.Success || dataResponse.Data == null)
            {
                return BadRequest(new {
                    error = "Failed to fetch market data",
                    details = dataResponse.ErrorMessage
                });
            }

            return Ok(new
            {
                symbol = dataResponse.Data.Symbol,
                price = dataResponse.Data.Price,
                companyName = dataResponse.Data.CompanyName,
                sector = dataResponse.Data.Sector,
                industry = dataResponse.Data.Industry,
                country = dataResponse.Data.Country,
                marketCap = dataResponse.Data.MarketCap,
                peRatio = dataResponse.Data.PERatio,
                dayChange = dataResponse.Data.DayChange,
                dayChangePercent = dataResponse.Data.DayChangePercent,
                volume = dataResponse.Data.Volume,
                dataSource = dataResponse.DataSource,
                fromCache = dataResponse.FromCache,
                dataFetchTime = dataResponse.ResponseTime.TotalMilliseconds,
                lastUpdated = dataResponse.Data.LastUpdated,
                isRealTime = dataResponse.Data.IsRealTime,
                newsCount = dataResponse.Data.RelatedNews.Count,
                hasEconomicData = dataResponse.Data.EconomicData != null,
                news = includeNews ? dataResponse.Data.RelatedNews.Take(5).Select(n => new {
                    title = n.Title,
                    summary = n.Summary,
                    publishedAt = n.PublishedAt,
                    source = n.Source,
                    sentimentScore = n.SentimentScore
                }) : null
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    private object CreateRawDataResponse(EnhancedMarketData data, object analysis)
    {
        // Extract recommendation score from analysis
        var recommendationScore = ExtractRecommendationScore(analysis);
        var targetPrice = ExtractTargetPrice(analysis, data.Price);
        var stopLoss = ExtractStopLoss(analysis, data.Price);
        var sentimentScore = ExtractSentimentScore(analysis);
        var riskLevel = ExtractRiskLevel(analysis);
        var trendDirection = ExtractTrendDirection(analysis);

        return new
        {
            Symbol = data.Symbol,
            CompanyName = data.CompanyName ?? "",
            CurrentPrice = data.Price,
            DayChange = data.DayChange,
            DayChangePercent = data.DayChangePercent,
            Volume = data.Volume ?? 0,
            MarketCap = data.MarketCap ?? 0,
            PERatio = data.PERatio,
            DividendYield = data.DividendYield ?? 0,
            Week52High = data.Price * 1.2, // Estimate 20% above current price
            Week52Low = data.Price * 0.8,  // Estimate 20% below current price
            RSI = data.RSI,
            SMA20 = data.RecentPrices.Count >= 20 ? data.RecentPrices.TakeLast(20).Average() : data.Price,
            SMA50 = data.RecentPrices.Count >= 50 ? data.RecentPrices.TakeLast(50).Average() : data.Price,
            SMA200 = data.RecentPrices.Count >= 200 ? data.RecentPrices.TakeLast(200).Average() : data.Price,
            Beta = 1.0, // Default beta
            Volatility = Math.Abs(data.DayChangePercent ?? 0) / 100.0, // Use day change as volatility estimate
            RecommendationScore = recommendationScore,
            TargetPrice = targetPrice,
            StopLoss = stopLoss,
            SentimentScore = sentimentScore,
            NewsCount = data.RelatedNews.Count,
            RiskLevel = riskLevel,
            TrendDirection = trendDirection,
            DataSource = data.DataSource,
            LastUpdated = data.LastUpdated
        };
    }

    private object CreateSummaryResponse(EnhancedMarketData data, object analysis)
    {
        var signal = ExtractSignal(analysis);
        var confidence = ExtractRecommendationScore(analysis);
        var targetPrice = ExtractTargetPrice(analysis, data.Price);
        var stopLoss = ExtractStopLoss(analysis, data.Price);
        var riskReward = targetPrice > data.Price ? (targetPrice - data.Price) / (data.Price - stopLoss) : 0;

        return new
        {
            Symbol = data.Symbol,
            Signal = signal,
            Confidence = confidence,
            EntryPrice = data.Price,
            TargetPrice = targetPrice,
            StopLoss = stopLoss,
            RiskReward = Math.Round(riskReward, 2),
            Position = signal == "BUY" ? "LONG" : signal == "SELL" ? "SHORT" : "NEUTRAL",
            Timeframe = "SWING",
            Strength = confidence >= 8 ? "STRONG" : confidence >= 6 ? "MODERATE" : "WEAK",
            Trend = ExtractTrendDirection(analysis) == 1 ? "BULLISH" : ExtractTrendDirection(analysis) == -1 ? "BEARISH" : "NEUTRAL",
            Support = Math.Round(data.Price * 0.95, 2), // Simple 5% below current price
            Resistance = Math.Round(data.Price * 1.05, 2), // Simple 5% above current price
            Momentum = data.DayChangePercent > 1 ? "POSITIVE" : data.DayChangePercent < -1 ? "NEGATIVE" : "NEUTRAL",
            Volume = data.Volume > 1000000 ? "HIGH" : data.Volume > 500000 ? "MEDIUM" : "LOW",
            Volatility = data.RSI > 70 || data.RSI < 30 ? "HIGH" : "MEDIUM",
            Timestamp = DateTime.UtcNow
        };
    }

    private object CreateTechnicalResponse(EnhancedMarketData data, object analysis)
    {
        return new
        {
            Symbol = data.Symbol,
            Technical = new
            {
                Trend = ExtractTrendDirection(analysis) == 1 ? "BULLISH" : ExtractTrendDirection(analysis) == -1 ? "BEARISH" : "NEUTRAL",
                RSI = data.RSI,
                MACD = data.DayChangePercent > 0 ? "POSITIVE" : "NEGATIVE",
                Support = Math.Round(data.Price * 0.95, 2),
                Resistance = Math.Round(data.Price * 1.05, 2),
                Signal = ExtractSignal(analysis)
            },
            Fundamental = new
            {
                PERatio = data.PERatio,
                Growth = data.DayChangePercent > 2 ? "STRONG" : data.DayChangePercent > 0 ? "MODERATE" : "WEAK",
                Valuation = data.PERatio < 20 ? "UNDERVALUED" : data.PERatio > 30 ? "OVERVALUED" : "FAIR",
                Quality = data.MarketCap > 100000000000 ? "HIGH" : "MEDIUM",
                Signal = ExtractSignal(analysis)
            },
            Risk = new
            {
                Level = ExtractRiskLevel(analysis) == 1 ? "LOW" : ExtractRiskLevel(analysis) == 2 ? "MEDIUM" : "HIGH",
                Beta = 1.0, // Default beta
                Volatility = Math.Abs(data.DayChangePercent ?? 0) / 100.0, // Use day change as volatility estimate
                MaxDrawdown = Math.Round(Math.Abs(data.DayChangePercent ?? 0), 1) // Use day change as drawdown estimate
            }
        };
    }

    // Helper methods to extract data from AI analysis
    private double ExtractRecommendationScore(object analysis)
    {
        // Parse the analysis result to extract a numeric score (1-10)
        // The analysis object has properties: bias, confidence, details
        try
        {
            var analysisType = analysis.GetType();
            var biasProperty = analysisType.GetProperty("bias");
            var confidenceProperty = analysisType.GetProperty("confidence");

            var bias = biasProperty?.GetValue(analysis)?.ToString()?.ToLower() ?? "";
            var confidence = (double)(confidenceProperty?.GetValue(analysis) ?? 0.5);

            // Convert bias and confidence to a 1-10 score
            var baseScore = bias switch
            {
                "buy" => 8.0,
                "sell" => 2.0,
                _ => 5.0 // HOLD
            };

            // Adjust by confidence (0-1 scale)
            return Math.Round(baseScore * confidence + (1 - confidence) * 5.0, 1);
        }
        catch
        {
            return 5.0; // Default neutral
        }
    }

    private string ExtractSignal(object analysis)
    {
        try
        {
            var analysisType = analysis.GetType();
            var biasProperty = analysisType.GetProperty("bias");
            var bias = biasProperty?.GetValue(analysis)?.ToString()?.ToUpper() ?? "HOLD";
            return bias;
        }
        catch
        {
            return "HOLD";
        }
    }

    private double ExtractTargetPrice(object analysis, double currentPrice)
    {
        // Simple logic based on recommendation score
        var score = ExtractRecommendationScore(analysis);
        var multiplier = score >= 7 ? 1.1 : score >= 5 ? 1.05 : 0.95;
        return Math.Round(currentPrice * multiplier, 2);
    }

    private double ExtractStopLoss(object analysis, double currentPrice)
    {
        // Simple 5-10% stop loss based on risk assessment
        var riskLevel = ExtractRiskLevel(analysis);
        var stopLossPercent = riskLevel == 3 ? 0.90 : riskLevel == 2 ? 0.93 : 0.95;
        return Math.Round(currentPrice * stopLossPercent, 2);
    }

    private double ExtractSentimentScore(object analysis)
    {
        // Extract sentiment from analysis confidence (0.0 to 1.0)
        try
        {
            var analysisType = analysis.GetType();
            var confidenceProperty = analysisType.GetProperty("confidence");
            var confidence = (double)(confidenceProperty?.GetValue(analysis) ?? 0.5);
            return Math.Round(confidence, 2);
        }
        catch
        {
            return 0.5; // Default neutral
        }
    }

    private int ExtractRiskLevel(object analysis)
    {
        // Return 1=Low, 2=Medium, 3=High based on confidence
        try
        {
            var analysisType = analysis.GetType();
            var confidenceProperty = analysisType.GetProperty("confidence");
            var confidence = (double)(confidenceProperty?.GetValue(analysis) ?? 0.5);

            return confidence switch
            {
                >= 0.8 => 1, // High confidence = Low risk
                >= 0.5 => 2, // Medium confidence = Medium risk
                _ => 3       // Low confidence = High risk
            };
        }
        catch
        {
            return 2; // Default medium risk
        }
    }

    private int ExtractTrendDirection(object analysis)
    {
        // Return -1=Bearish, 0=Neutral, 1=Bullish
        var signal = ExtractSignal(analysis);
        return signal switch
        {
            "BUY" => 1,
            "SELL" => -1,
            _ => 0
        };
    }
    }
}