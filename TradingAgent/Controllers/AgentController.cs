// Controllers/AgentController.cs
using Microsoft.AspNetCore.Mvc;
using TradingAgent.Models;
using Core;
using TradingAgent.Services;
using TradingAgent.Core;

namespace TradingAgent.Controllers
{
    [ApiController]
    [Route("/analyze")]
    public class AgentController : ControllerBase
{
    private readonly IAgentEngine _engine;
    private readonly IDataAggregationService _dataService;

    public AgentController(IAgentEngine engine, IDataAggregationService dataService)
    {
        _engine = engine;
        _dataService = dataService;
    }

    /// <summary>
    /// Analyze market data (supports both manual data input and automatic fetching)
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Analyze([FromBody] MarketData data)
    {
        var result = await _engine.RunAsync(data);
        return Ok(result);
    }

    /// <summary>
    /// Analyze a symbol by automatically fetching all required data
    /// </summary>
    [HttpPost("symbol/{symbol}")]
    public async Task<IActionResult> AnalyzeSymbol(
        string symbol, 
        [FromQuery] bool includeNews = true,
        [FromQuery] bool includeEconomicData = true,
        [FromQuery] int newsLookbackDays = 7,
        [FromQuery] string? preferredDataSource = null,
        [FromQuery] bool forceRefresh = false)
    {
        try
        {
            var request = new MarketDataRequest
            {
                Symbol = symbol.ToUpper(),
                IncludeNews = includeNews,
                IncludeEconomicData = includeEconomicData,
                NewsLookbackDays = newsLookbackDays,
                PreferredDataSource = preferredDataSource,
                ForceRefresh = forceRefresh
            };

            var dataResponse = await _dataService.GetCompleteMarketDataAsync(request);
            
            if (!dataResponse.Success || dataResponse.Data == null)
            {
                return BadRequest(new { 
                    error = "Failed to fetch market data", 
                    details = dataResponse.ErrorMessage 
                });
            }

            var analysisResult = await _engine.RunAsync(dataResponse.Data);
            
            return Ok(new
            {
                analysis = analysisResult,
                dataMetadata = new
                {
                    dataSource = dataResponse.DataSource,
                    fromCache = dataResponse.FromCache,
                    dataFetchTime = dataResponse.ResponseTime.TotalMilliseconds,
                    lastUpdated = dataResponse.Data.LastUpdated,
                    isRealTime = dataResponse.Data.IsRealTime,
                    newsCount = dataResponse.Data.RelatedNews.Count,
                    hasEconomicData = dataResponse.Data.EconomicData != null
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Get available symbols for analysis
    /// </summary>
    [HttpGet("symbols")]
    public async Task<IActionResult> GetAvailableSymbols([FromQuery] string? sector = null)
    {
        try
        {
            var symbols = await _dataService.GetAvailableSymbolsAsync(sector);
            return Ok(new { symbols, sector });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to fetch symbols", details = ex.Message });
        }
    }

    /// <summary>
    /// Get health status of all data providers
    /// </summary>
    [HttpGet("health")]
    public async Task<IActionResult> GetProviderHealth()
    {
        try
        {
            var healthStatus = await _dataService.GetProviderHealthStatusAsync();
            var overallHealth = healthStatus.Values.Any(h => h);

            return Ok(new {
                overallHealthy = overallHealth,
                providers = healthStatus,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to check provider health", details = ex.Message });
        }
    }

    /// <summary>
    /// Test endpoint to get raw market data without AI analysis
    /// </summary>
    [HttpGet("data/{symbol}")]
    public async Task<IActionResult> GetMarketData(
        string symbol,
        [FromQuery] bool includeNews = false,
        [FromQuery] bool includeEconomicData = false,
        [FromQuery] int newsLookbackDays = 7,
        [FromQuery] string? preferredDataSource = null,
        [FromQuery] bool forceRefresh = false)
    {
        try
        {
            var request = new MarketDataRequest
            {
                Symbol = symbol.ToUpper(),
                IncludeNews = includeNews,
                IncludeEconomicData = includeEconomicData,
                NewsLookbackDays = newsLookbackDays,
                PreferredDataSource = preferredDataSource,
                ForceRefresh = forceRefresh
            };

            var dataResponse = await _dataService.GetCompleteMarketDataAsync(request);

            if (!dataResponse.Success || dataResponse.Data == null)
            {
                return BadRequest(new {
                    error = "Failed to fetch market data",
                    details = dataResponse.ErrorMessage
                });
            }

            return Ok(new
            {
                symbol = dataResponse.Data.Symbol,
                price = dataResponse.Data.Price,
                companyName = dataResponse.Data.CompanyName,
                sector = dataResponse.Data.Sector,
                industry = dataResponse.Data.Industry,
                country = dataResponse.Data.Country,
                marketCap = dataResponse.Data.MarketCap,
                peRatio = dataResponse.Data.PERatio,
                dayChange = dataResponse.Data.DayChange,
                dayChangePercent = dataResponse.Data.DayChangePercent,
                volume = dataResponse.Data.Volume,
                dataSource = dataResponse.DataSource,
                fromCache = dataResponse.FromCache,
                dataFetchTime = dataResponse.ResponseTime.TotalMilliseconds,
                lastUpdated = dataResponse.Data.LastUpdated,
                isRealTime = dataResponse.Data.IsRealTime,
                newsCount = dataResponse.Data.RelatedNews.Count,
                hasEconomicData = dataResponse.Data.EconomicData != null,
                news = includeNews ? dataResponse.Data.RelatedNews.Take(5).Select(n => new {
                    title = n.Title,
                    summary = n.Summary,
                    publishedAt = n.PublishedAt,
                    source = n.Source,
                    sentimentScore = n.SentimentScore
                }) : null
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }
}
}