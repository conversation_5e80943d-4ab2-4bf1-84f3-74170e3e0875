// Controllers/ModelController.cs
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Config;
using TradingAgent.Models;

namespace TradingAgent.Controllers
{
    [ApiController]
    [Route("/models")]
    public class ModelController : ControllerBase
    {
        private readonly IOptionsMonitor<OpenAISettings> _settingsMonitor;

        public ModelController(IOptionsMonitor<OpenAISettings> settingsMonitor)
        {
            _settingsMonitor = settingsMonitor;
        }

        /// <summary>
        /// Get all available GPT models with detailed information
        /// </summary>
        [HttpGet("available")]
        public IActionResult GetAvailableModels()
        {
            var modelDetails = GptModels.All.Select(model => new
            {
                id = model,
                info = GptModels.GetModelInfo(model)
            }).ToArray();

            return Ok(new
            {
                models = modelDetails,
                defaultModel = GptModels.Default,
                openAIModels = GptModels.OpenAIModels.ToArray(),
                localModels = GptModels.LocalModels.ToArray(),
                supportedProviders = new[] { "OpenAI", "Local" }
            });
        }

        /// <summary>
        /// Get current model configuration
        /// </summary>
        [HttpGet("configuration")]
        public IActionResult GetModelConfiguration()
        {
            var settings = _settingsMonitor.CurrentValue;

            return Ok(new
            {
                defaultModel = settings.DefaultModel,
                agentModels = settings.AgentModels,
                baseUrl = settings.BaseUrl,
                timeoutSeconds = settings.TimeoutSeconds,
                maxRetries = settings.MaxRetries
            });
        }

        /// <summary>
        /// Update agent model configuration
        /// </summary>
        [HttpPut("agent/{agentName}")]
        public IActionResult UpdateAgentModel(string agentName, [FromBody] UpdateModelRequest request)
        {
            if (!GptModels.All.Contains(request.Model))
            {
                return BadRequest($"Model '{request.Model}' is not supported. Available models: {string.Join(", ", GptModels.All)}");
            }

            // Note: This would typically update a configuration store
            // For now, we'll return what the change would be
            return Ok(new
            {
                message = $"Agent '{agentName}' model would be updated to '{request.Model}'",
                agentName,
                newModel = request.Model,
                note = "To persist changes, update appsettings.json and restart the application"
            });
        }

        /// <summary>
        /// Test model connectivity
        /// </summary>
        [HttpPost("test")]
        public async Task<IActionResult> TestModel([FromBody] TestModelRequest request, [FromServices] Services.OpenAIClient openAIClient)
        {
            try
            {
                var testPrompt = "Say 'Model test successful' if you can read this.";
                var response = await openAIClient.AskAsync(testPrompt, request.Model);

                return Ok(new
                {
                    success = true,
                    model = request.Model,
                    response = response,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    model = request.Model,
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }
    }

    public class UpdateModelRequest
    {
        public string Model { get; set; } = string.Empty;
    }

    public class TestModelRequest
    {
        public string Model { get; set; } = GptModels.Default;
    }
}