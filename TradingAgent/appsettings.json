{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "OpenAI": {"ApiKey": "", "DefaultModel": "gpt-4o", "BaseUrl": "https://api.openai.com/v1", "MaxRetries": 3, "TimeoutSeconds": 30, "AgentModels": {"FundamentalAnalyst": "gpt-4o", "SentimentAnalyst": "gpt-3.5-turbo", "NewsAnalyst": "gpt-4o", "RiskAnalyst": "gpt-4"}}, "DataProviders": {"PrimaryMarketDataProvider": "YahooFinance", "PrimaryNewsProvider": "NewsAPI", "MarketDataCacheMinutes": 5, "NewsCacheMinutes": 15, "EnableFallback": true, "AlphaVantage": {"ApiKey": "", "BaseUrl": "https://www.alphavantage.co/query", "RateLimitPerMinute": 75, "TimeoutSeconds": 30}, "Finnhub": {"ApiKey": "", "BaseUrl": "https://finnhub.io/api/v1", "RateLimitPerMinute": 60, "RateLimitPerSecond": 30, "TimeoutSeconds": 30}, "Polygon": {"ApiKey": "", "BaseUrl": "https://api.polygon.io", "RateLimitPerMinute": 5, "TimeoutSeconds": 30}, "NewsApi": {"ApiKey": "", "BaseUrl": "https://newsapi.org/v2", "RateLimitPerDay": 500, "TimeoutSeconds": 30}, "Fred": {"ApiKey": "", "BaseUrl": "https://api.stlouisfed.org/fred", "TimeoutSeconds": 30}, "YahooFinance": {"BaseUrl": "https://query1.finance.yahoo.com", "RateLimitPerMinute": 2000, "TimeoutSeconds": 30, "EnableRealTime": true}}}