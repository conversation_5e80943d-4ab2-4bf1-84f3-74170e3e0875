<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <!-- Temporarily disabled OpenAPI/Swagger for .NET 10 compatibility testing -->
    <!-- <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="10.0.0-preview.6.25358.103" /> -->
    <!-- <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" /> -->
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Tests/**" />
    <Content Remove="Tests/**" />
    <EmbeddedResource Remove="Tests/**" />
    <None Remove="Tests/**" />
  </ItemGroup>

</Project>
