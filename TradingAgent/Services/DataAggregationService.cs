// Services/DataAggregationService.cs
using Config;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TradingAgent.Models;

namespace TradingAgent.Services
{
    public class DataAggregationService(
        IEnumerable<IMarketDataProvider> marketDataProviders,
        IEnumerable<INewsProvider> newsProviders,
        IEnumerable<IEconomicDataProvider> economicProviders,
        IOptions<DataProviderSettings> settings,
        ILogger<DataAggregationService> logger,
        IMemoryCache cache) : IDataAggregationService
    {
        private readonly IEnumerable<IMarketDataProvider> _marketDataProviders = marketDataProviders;
        private readonly IEnumerable<INewsProvider> _newsProviders = newsProviders;
        private readonly IEnumerable<IEconomicDataProvider> _economicProviders = economicProviders;
        private readonly DataProviderSettings _settings = settings.Value;
        private readonly ILogger<DataAggregationService> _logger = logger;
        private readonly IMemoryCache _cache = cache;

        public async Task<DataProviderResponse<EnhancedMarketData>> GetCompleteMarketDataAsync(MarketDataRequest request, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var cacheKey = $"market_data_{request.Symbol}_{request.IncludeNews}_{request.IncludeEconomicData}";
                
                // Check cache first (unless force refresh is requested)
                if (!request.ForceRefresh && _cache.TryGetValue(cacheKey, out EnhancedMarketData? cachedData) && cachedData != null)
                {
                    _logger.LogDebug("Returning cached market data for {Symbol}", request.Symbol);
                    return new DataProviderResponse<EnhancedMarketData>
                    {
                        Data = cachedData,
                        Success = true,
                        DataSource = "Cache",
                        FromCache = true,
                        ResponseTime = stopwatch.Elapsed
                    };
                }

                _logger.LogInformation("Fetching complete market data for {Symbol}", request.Symbol);

                // Fetch market data
                var marketDataTask = GetMarketDataWithFallback(request.Symbol, request.PreferredDataSource, cancellationToken);
                
                // Fetch news if requested
                Task<List<NewsItem>>? newsTask = null;
                if (request.IncludeNews)
                {
                    newsTask = GetNewsWithFallback(request.Symbol, request.NewsLookbackDays, cancellationToken);
                }

                // Fetch economic data if requested
                Task<EconomicIndicators?>? economicTask = null;
                if (request.IncludeEconomicData)
                {
                    economicTask = GetEconomicDataWithFallback(cancellationToken);
                }

                // Wait for all tasks to complete
                var marketData = await marketDataTask;
                var news = newsTask != null ? await newsTask : new List<NewsItem>();
                var economicData = economicTask != null ? await economicTask : null;

                if (marketData == null)
                {
                    return new DataProviderResponse<EnhancedMarketData>
                    {
                        Success = false,
                        ErrorMessage = "Failed to fetch market data from all providers",
                        ResponseTime = stopwatch.Elapsed
                    };
                }

                // Enhance market data with additional information
                marketData.RelatedNews = news;
                marketData.EconomicData = economicData;
                
                // Enrich with aggregated news sentiment
                if (news.Any())
                {
                    var avgSentiment = news.Average(n => n.SentimentScore);
                    var newsText = string.Join(" ", news.Take(5).Select(n => n.Title));
                    marketData.RawNews = $"Sentiment: {avgSentiment:F2}. Recent: {newsText}";
                }

                // Cache the result
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_settings.MarketDataCacheMinutes),
                    SlidingExpiration = TimeSpan.FromMinutes(_settings.MarketDataCacheMinutes / 2)
                };
                _cache.Set(cacheKey, marketData, cacheOptions);

                return new DataProviderResponse<EnhancedMarketData>
                {
                    Data = marketData,
                    Success = true,
                    DataSource = marketData.DataSource,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch complete market data for {Symbol}", request.Symbol);
                return new DataProviderResponse<EnhancedMarketData>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public Task<List<string>> GetAvailableSymbolsAsync(string? sector = null, CancellationToken cancellationToken = default)
        {
            // For now, return a curated list of popular symbols
            // In a real implementation, this would fetch from a symbols API
            var popularSymbols = new List<string>
            {
                "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "V", "JPM",
                "JNJ", "WMT", "PG", "UNH", "HD", "MA", "DIS", "PYPL", "ADBE", "CRM",
                "VZ", "INTC", "PFE", "T", "KO", "PEP", "ABT", "TMO", "COST", "AVGO"
            };

            // Simple sector filtering (would be more sophisticated in real implementation)
            if (!string.IsNullOrEmpty(sector))
            {
                var sectorSymbols = sector.ToLower() switch
                {
                    "technology" => ["AAPL", "MSFT", "GOOGL", "META", "NVDA", "ADBE", "CRM", "INTC"],
                    "healthcare" => ["JNJ", "UNH", "PFE", "ABT", "TMO"],
                    "financial" => ["JPM", "V", "MA", "PYPL"],
                    "consumer" => ["AMZN", "WMT", "HD", "DIS", "KO", "PEP", "COST"],
                    "energy" => ["XOM", "CVX", "COP", "EOG"],
                    _ => popularSymbols.ToArray()
                };
                
                return Task.FromResult(sectorSymbols.ToList());
            }

            return Task.FromResult(popularSymbols);
        }

        public async Task<Dictionary<string, bool>> GetProviderHealthStatusAsync()
        {
            var healthStatus = new Dictionary<string, bool>();
            var tasks = new List<Task<(string provider, bool healthy)>>();

            // Check market data providers
            foreach (var provider in _marketDataProviders)
            {
                tasks.Add(CheckProviderHealth(provider.ProviderName, provider.IsHealthyAsync));
            }

            // Check news providers
            foreach (var provider in _newsProviders)
            {
                tasks.Add(CheckProviderHealth(provider.ProviderName, provider.IsHealthyAsync));
            }

            // Check economic data providers
            foreach (var provider in _economicProviders)
            {
                tasks.Add(CheckProviderHealth(provider.ProviderName, provider.IsHealthyAsync));
            }

            var results = await Task.WhenAll(tasks);
            
            foreach (var (provider, healthy) in results)
            {
                healthStatus[provider] = healthy;
            }

            return healthStatus;
        }

        private async Task<EnhancedMarketData?> GetMarketDataWithFallback(string symbol, string? preferredProvider, CancellationToken cancellationToken)
        {
            var providers = _marketDataProviders.ToList();
            
            // Move preferred provider to front if specified
            if (!string.IsNullOrEmpty(preferredProvider))
            {
                var preferred = providers.FirstOrDefault(p => p.ProviderName.Equals(preferredProvider, StringComparison.OrdinalIgnoreCase));
                if (preferred != null)
                {
                    providers.Remove(preferred);
                    providers.Insert(0, preferred);
                }
            }
            else
            {
                // Use configured primary provider
                var primary = providers.FirstOrDefault(p => p.ProviderName.Equals(_settings.PrimaryMarketDataProvider, StringComparison.OrdinalIgnoreCase));
                if (primary != null)
                {
                    providers.Remove(primary);
                    providers.Insert(0, primary);
                }
            }

            foreach (var provider in providers)
            {
                try
                {
                    _logger.LogDebug("Attempting to fetch market data from {Provider}", provider.ProviderName);
                    var result = await provider.GetMarketDataAsync(symbol, cancellationToken);
                    
                    if (result.Success && result.Data != null)
                    {
                        _logger.LogDebug("Successfully fetched market data from {Provider}", provider.ProviderName);
                        return result.Data;
                    }
                    
                    _logger.LogWarning("Failed to fetch market data from {Provider}: {Error}", provider.ProviderName, result.ErrorMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error fetching market data from {Provider}", provider.ProviderName);
                }

                if (!_settings.EnableFallback) break;
            }

            return null;
        }

        private async Task<List<NewsItem>> GetNewsWithFallback(string symbol, int lookbackDays, CancellationToken cancellationToken)
        {
            var providers = _newsProviders.ToList();
            
            // Use configured primary news provider first
            var primary = providers.FirstOrDefault(p => p.ProviderName.Equals(_settings.PrimaryNewsProvider, StringComparison.OrdinalIgnoreCase));
            if (primary != null)
            {
                providers.Remove(primary);
                providers.Insert(0, primary);
            }

            foreach (var provider in providers)
            {
                try
                {
                    _logger.LogDebug("Attempting to fetch news from {Provider}", provider.ProviderName);
                    var result = await provider.GetNewsAsync(symbol, lookbackDays, cancellationToken);
                    
                    if (result.Success && result.Data != null)
                    {
                        _logger.LogDebug("Successfully fetched {Count} news items from {Provider}", result.Data.Count, provider.ProviderName);
                        return result.Data;
                    }
                    
                    _logger.LogWarning("Failed to fetch news from {Provider}: {Error}", provider.ProviderName, result.ErrorMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error fetching news from {Provider}", provider.ProviderName);
                }

                if (!_settings.EnableFallback) break;
            }

            return new List<NewsItem>();
        }

        private async Task<EconomicIndicators?> GetEconomicDataWithFallback(CancellationToken cancellationToken)
        {
            var cacheKey = "economic_indicators";
            
            // Check cache first
            if (_cache.TryGetValue(cacheKey, out EconomicIndicators? cachedIndicators) && cachedIndicators != null)
            {
                return cachedIndicators;
            }

            foreach (var provider in _economicProviders)
            {
                try
                {
                    _logger.LogDebug("Attempting to fetch economic data from {Provider}", provider.ProviderName);
                    var result = await provider.GetEconomicIndicatorsAsync(cancellationToken);
                    
                    if (result.Success && result.Data != null)
                    {
                        _logger.LogDebug("Successfully fetched economic data from {Provider}", provider.ProviderName);
                        
                        // Cache economic data for longer period
                        var cacheOptions = new MemoryCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1), // Economic data changes less frequently
                            SlidingExpiration = TimeSpan.FromMinutes(30)
                        };
                        _cache.Set(cacheKey, result.Data, cacheOptions);
                        
                        return result.Data;
                    }
                    
                    _logger.LogWarning("Failed to fetch economic data from {Provider}: {Error}", provider.ProviderName, result.ErrorMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error fetching economic data from {Provider}", provider.ProviderName);
                }

                if (!_settings.EnableFallback) break;
            }

            return null;
        }

        private static async Task<(string provider, bool healthy)> CheckProviderHealth(string providerName, Func<Task<bool>> healthCheck)
        {
            try
            {
                var healthy = await healthCheck();
                return (providerName, healthy);
            }
            catch
            {
                return (providerName, false);
            }
        }
    }
}