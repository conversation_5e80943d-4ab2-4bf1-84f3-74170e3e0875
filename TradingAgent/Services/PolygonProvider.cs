// Services/PolygonProvider.cs
using System.Text.Json;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Config;
using TradingAgent.Models;

namespace TradingAgent.Services
{
    public class PolygonProvider : IMarketDataProvider, INewsProvider
    {
        private readonly HttpClient _httpClient;
        private readonly PolygonSettings _settings;
        private readonly ILogger<PolygonProvider> _logger;

        public string ProviderName => "Polygon";

        public PolygonProvider(HttpClient httpClient, IOptions<DataProviderSettings> settings, ILogger<PolygonProvider> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.Polygon;
            _logger = logger;
            
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
        }

        public async Task<DataProviderResponse<EnhancedMarketData>> GetMarketDataAsync(string symbol, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                // Use secondary API key for market data if configured
                var apiKey = _settings.UseSecondaryForMarketData && !string.IsNullOrEmpty(_settings.ApiKeySecondary) 
                    ? _settings.ApiKeySecondary 
                    : _settings.ApiKey;

                if (string.IsNullOrEmpty(apiKey))
                {
                    throw new InvalidOperationException("No Polygon.io API key configured for market data");
                }

                // Get previous day's data (most recent complete trading day)
                var prevDayUrl = $"{_settings.BaseUrl}/v2/aggs/ticker/{symbol}/prev?adjusted=true&apikey={apiKey}";
                var prevDayResponse = await _httpClient.GetStringAsync(prevDayUrl, cancellationToken);
                
                // Get company details
                var detailsUrl = $"{_settings.BaseUrl}/v3/reference/tickers/{symbol}?apikey={apiKey}";
                var detailsResponse = await _httpClient.GetStringAsync(detailsUrl, cancellationToken);

                var marketData = ParseMarketData(symbol, prevDayResponse, detailsResponse);

                return new DataProviderResponse<EnhancedMarketData>
                {
                    Data = marketData,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch market data for {Symbol} from Polygon", symbol);
                return new DataProviderResponse<EnhancedMarketData>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<double>>> GetHistoricalPricesAsync(string symbol, int days, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                // Use secondary API key for market data
                var apiKey = _settings.UseSecondaryForMarketData && !string.IsNullOrEmpty(_settings.ApiKeySecondary) 
                    ? _settings.ApiKeySecondary 
                    : _settings.ApiKey;

                var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
                var startDate = DateTime.UtcNow.AddDays(-days).ToString("yyyy-MM-dd");
                
                var url = $"{_settings.BaseUrl}/v2/aggs/ticker/{symbol}/range/1/day/{startDate}/{endDate}?adjusted=true&sort=desc&apikey={apiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var prices = ParseHistoricalPrices(response);

                return new DataProviderResponse<List<double>>
                {
                    Data = prices,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch historical prices for {Symbol} from Polygon", symbol);
                return new DataProviderResponse<List<double>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetNewsAsync(string symbol, int lookbackDays, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                // Use primary API key for news data
                var apiKey = _settings.ApiKey;

                if (string.IsNullOrEmpty(apiKey))
                {
                    throw new InvalidOperationException("No Polygon.io API key configured for news data");
                }

                var publishedDate = DateTime.UtcNow.AddDays(-lookbackDays).ToString("yyyy-MM-dd");
                
                var url = $"{_settings.BaseUrl}/v2/reference/news?ticker={symbol}&published_utc.gte={publishedDate}&order=desc&limit=50&apikey={apiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var newsItems = ParseNews(response, symbol);

                return new DataProviderResponse<List<NewsItem>>
                {
                    Data = newsItems,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch news for {Symbol} from Polygon", symbol);
                return new DataProviderResponse<List<NewsItem>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetMarketNewsAsync(int lookbackDays, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                // Use primary API key for news data
                var apiKey = _settings.ApiKey;
                var publishedDate = DateTime.UtcNow.AddDays(-lookbackDays).ToString("yyyy-MM-dd");
                
                var url = $"{_settings.BaseUrl}/v2/reference/news?published_utc.gte={publishedDate}&order=desc&limit=50&apikey={apiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var newsItems = ParseNews(response, "MARKET");

                return new DataProviderResponse<List<NewsItem>>
                {
                    Data = newsItems,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch market news from Polygon");
                return new DataProviderResponse<List<NewsItem>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                // Test with primary key first
                var apiKey = !string.IsNullOrEmpty(_settings.ApiKey) ? _settings.ApiKey : _settings.ApiKeySecondary;
                var url = $"{_settings.BaseUrl}/v2/aggs/ticker/AAPL/prev?adjusted=true&apikey={apiKey}";
                var response = await _httpClient.GetStringAsync(url);
                
                using var doc = JsonDocument.Parse(response);
                return doc.RootElement.GetProperty("status").GetString() == "OK";
            }
            catch
            {
                return false;
            }
        }

        private EnhancedMarketData ParseMarketData(string symbol, string prevDayJson, string detailsJson)
        {
            using var prevDayDoc = JsonDocument.Parse(prevDayJson);
            
            var results = prevDayDoc.RootElement.GetProperty("results");
            if (results.GetArrayLength() == 0)
            {
                throw new InvalidOperationException($"No market data found for {symbol}");
            }

            var result = results[0];
            
            var closePrice = result.GetProperty("c").GetDouble();
            var openPrice = result.GetProperty("o").GetDouble();

            var marketData = new EnhancedMarketData
            {
                Symbol = symbol,
                Price = closePrice, // Close price
                Volume = result.GetProperty("v").GetDouble(),
                DayChange = closePrice - openPrice,
                DayChangePercent = ((closePrice - openPrice) / openPrice) * 100,
                DataSource = ProviderName,
                LastUpdated = DateTime.UtcNow,
                IsRealTime = false,
                RecentPrices = new List<double> { closePrice }
            };

            // Try to parse company details
            try
            {
                using var detailsDoc = JsonDocument.Parse(detailsJson);
                var details = detailsDoc.RootElement.GetProperty("results");

                marketData.CompanyName = details.GetProperty("name").GetString() ?? symbol;
                marketData.Sector = details.GetProperty("sic_description").GetString() ?? "Unknown";

                // Set additional properties if available
                if (details.TryGetProperty("market_cap", out var marketCapElement))
                {
                    marketData.MarketCap = marketCapElement.GetDouble();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to parse company details: {Error}", ex.Message);
                marketData.CompanyName = symbol;
                marketData.Sector = "Unknown";
            }

            return marketData;
        }

        private List<double> ParseHistoricalPrices(string json)
        {
            var prices = new List<double>();
            
            try
            {
                using var doc = JsonDocument.Parse(json);
                var results = doc.RootElement.GetProperty("results");
                
                foreach (var result in results.EnumerateArray())
                {
                    prices.Add(result.GetProperty("c").GetDouble()); // Close price
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to parse historical prices: {Error}", ex.Message);
            }

            return prices;
        }

        private List<NewsItem> ParseNews(string json, string symbol)
        {
            var newsItems = new List<NewsItem>();
            
            try
            {
                using var doc = JsonDocument.Parse(json);
                var results = doc.RootElement.GetProperty("results");
                
                foreach (var article in results.EnumerateArray())
                {
                    var newsItem = new NewsItem
                    {
                        Title = article.GetProperty("title").GetString() ?? "",
                        Summary = article.GetProperty("description").GetString() ?? "",
                        Source = article.GetProperty("publisher").GetProperty("name").GetString() ?? "Polygon",
                        PublishedAt = DateTime.Parse(article.GetProperty("published_utc").GetString() ?? DateTime.UtcNow.ToString()),
                        Url = article.GetProperty("article_url").GetString() ?? "",
                        SentimentScore = 0.0, // Default neutral sentiment
                        Keywords = new List<string>() // Empty keywords list
                    };
                    
                    newsItems.Add(newsItem);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to parse news data: {Error}", ex.Message);
            }

            return newsItems;
        }
    }
}
