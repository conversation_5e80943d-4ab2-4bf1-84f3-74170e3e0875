// Services/FredEconomicProvider.cs
using System.Text.Json;
using Config;
using Microsoft.Extensions.Options;
using TradingAgent.Models;

namespace TradingAgent.Services
{
    public class FredEconomicProvider : IEconomicDataProvider
    {
        private readonly HttpClient _httpClient;
        private readonly FredSettings _settings;
        private readonly ILogger<FredEconomicProvider> _logger;

        public string ProviderName => "FRED";

        // FRED series IDs for economic indicators
        private readonly Dictionary<string, string> _seriesIds = new()
        {
            ["FedFundsRate"] = "FEDFUNDS",
            ["InflationRate"] = "CPIAUCSL",
            ["UnemploymentRate"] = "UNRATE",
            ["GDP_Growth"] = "GDP",
            ["VIX"] = "VIXCLS",
            ["DXY"] = "DEXUSEU", // Euro/USD exchange rate as proxy for DXY
            ["CrudeOil"] = "DCOILWTICO",
            ["Gold"] = "GOLDAMGBD228NLBM"
        };

        public FredEconomicProvider(HttpClient httpClient, IOptions<DataProviderSettings> settings, ILogger<FredEconomicProvider> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.Fred;
            _logger = logger;
            
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
        }

        public async Task<DataProviderResponse<EconomicIndicators>> GetEconomicIndicatorsAsync(CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var indicators = new EconomicIndicators();
                var tasks = new List<Task>();

                // Fetch all economic indicators in parallel
                tasks.Add(FetchAndSetIndicator("FedFundsRate", value => indicators.FedFundsRate = value, cancellationToken));
                tasks.Add(FetchAndSetIndicator("UnemploymentRate", value => indicators.UnemploymentRate = value, cancellationToken));
                tasks.Add(FetchAndSetIndicator("VIX", value => indicators.VIX = value, cancellationToken));
                tasks.Add(FetchAndSetIndicator("CrudeOil", value => indicators.CrudeOilPrice = value, cancellationToken));
                tasks.Add(FetchAndSetIndicator("Gold", value => indicators.GoldPrice = value, cancellationToken));

                // Special handling for inflation rate (needs calculation)
                tasks.Add(FetchInflationRate(value => indicators.InflationRate = value, cancellationToken));

                await Task.WhenAll(tasks);
                
                indicators.LastUpdated = DateTime.UtcNow;

                return new DataProviderResponse<EconomicIndicators>
                {
                    Data = indicators,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch economic indicators from FRED");
                return new DataProviderResponse<EconomicIndicators>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                var url = $"{_settings.BaseUrl}/series?series_id=FEDFUNDS&api_key={_settings.ApiKey}&file_type=json&limit=1";
                var response = await _httpClient.GetStringAsync(url);
                
                using var doc = JsonDocument.Parse(response);
                return !doc.RootElement.TryGetProperty("error_message", out _);
            }
            catch
            {
                return false;
            }
        }

        private async Task FetchAndSetIndicator(string indicatorName, Action<double> setter, CancellationToken cancellationToken)
        {
            try
            {
                if (!_seriesIds.TryGetValue(indicatorName, out var seriesId))
                {
                    _logger.LogWarning("Unknown economic indicator: {Indicator}", indicatorName);
                    return;
                }

                var url = $"{_settings.BaseUrl}/series/observations?" +
                         $"series_id={seriesId}&" +
                         $"api_key={_settings.ApiKey}&" +
                         $"file_type=json&" +
                         $"limit=1&" +
                         $"sort_order=desc";

                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                using var doc = JsonDocument.Parse(response);
                
                var observations = doc.RootElement.GetProperty("observations");
                if (observations.GetArrayLength() > 0)
                {
                    var latestValue = observations[0].GetProperty("value").GetString();
                    if (double.TryParse(latestValue, out var value) && latestValue != ".")
                    {
                        setter(value);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch {Indicator} from FRED", indicatorName);
            }
        }

        private async Task FetchInflationRate(Action<double> setter, CancellationToken cancellationToken)
        {
            try
            {
                // Get last 12 months of CPI data to calculate year-over-year inflation
                var url = $"{_settings.BaseUrl}/series/observations?" +
                         $"series_id=CPIAUCSL&" +
                         $"api_key={_settings.ApiKey}&" +
                         $"file_type=json&" +
                         $"limit=13&" + // 13 months to get year-over-year
                         $"sort_order=desc";

                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                using var doc = JsonDocument.Parse(response);
                
                var observations = doc.RootElement.GetProperty("observations");
                if (observations.GetArrayLength() >= 13)
                {
                    var latestCPI = observations[0].GetProperty("value").GetString();
                    var yearAgoCPI = observations[12].GetProperty("value").GetString();

                    if (double.TryParse(latestCPI, out var current) && 
                        double.TryParse(yearAgoCPI, out var yearAgo) &&
                        latestCPI != "." && yearAgoCPI != ".")
                    {
                        var inflationRate = ((current - yearAgo) / yearAgo) * 100;
                        setter(inflationRate);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to calculate inflation rate from FRED CPI data");
            }
        }
    }
}