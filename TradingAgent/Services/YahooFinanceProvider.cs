// Services/YahooFinanceProvider.cs
using System.Text.Json;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Config;
using TradingAgent.Models;
using System.Net;

namespace TradingAgent.Services
{
    public class YahooFinanceProvider : IMarketDataProvider
    {
        private readonly HttpClient _httpClient;
        private readonly YahooFinanceSettings _settings;
        private readonly ILogger<YahooFinanceProvider> _logger;
        private static readonly SemaphoreSlim _rateLimitSemaphore = new(10, 10); // Allow 10 concurrent requests
        private static DateTime _lastRequestTime = DateTime.MinValue;
        private static readonly object _rateLimitLock = new();

        public string ProviderName => "YahooFinance";

        public YahooFinanceProvider(HttpClient httpClient, IOptions<DataProviderSettings> settings, ILogger<YahooFinanceProvider> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.YahooFinance;
            _logger = logger;

            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);

            // Set multiple User-Agent headers to avoid blocking
            var userAgents = new[]
            {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            };

            var randomUserAgent = userAgents[new Random().Next(userAgents.Length)];
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", randomUserAgent);
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.5");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate");
            _httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");
            _httpClient.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
        }

        private async Task RateLimitAsync()
        {
            await _rateLimitSemaphore.WaitAsync();

            try
            {
                lock (_rateLimitLock)
                {
                    var timeSinceLastRequest = DateTime.UtcNow - _lastRequestTime;
                    var minInterval = TimeSpan.FromMilliseconds(100); // Minimum 100ms between requests

                    if (timeSinceLastRequest < minInterval)
                    {
                        var delay = minInterval - timeSinceLastRequest;
                        Thread.Sleep(delay);
                    }

                    _lastRequestTime = DateTime.UtcNow;
                }
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        private async Task<string> MakeRequestWithRetryAsync(string url, CancellationToken cancellationToken, int maxRetries = 3)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    await RateLimitAsync();

                    var response = await _httpClient.GetAsync(url, cancellationToken);

                    if (response.IsSuccessStatusCode)
                    {
                        return await response.Content.ReadAsStringAsync(cancellationToken);
                    }

                    if (response.StatusCode == HttpStatusCode.TooManyRequests ||
                        response.StatusCode == HttpStatusCode.ServiceUnavailable)
                    {
                        var retryAfter = response.Headers.RetryAfter?.Delta ?? TimeSpan.FromSeconds(Math.Pow(2, attempt));
                        _logger.LogWarning("Rate limited by Yahoo Finance. Waiting {Delay}s before retry {Attempt}/{MaxRetries}",
                            retryAfter.TotalSeconds, attempt, maxRetries);

                        if (attempt < maxRetries)
                        {
                            await Task.Delay(retryAfter, cancellationToken);
                            continue;
                        }
                    }

                    var content = await response.Content.ReadAsStringAsync(cancellationToken);
                    throw new HttpRequestException($"Yahoo Finance API returned {response.StatusCode}: {content}");
                }
                catch (Exception ex) when (attempt < maxRetries && !(ex is OperationCanceledException))
                {
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt));
                    _logger.LogWarning(ex, "Request failed (attempt {Attempt}/{MaxRetries}). Retrying in {Delay}s",
                        attempt, maxRetries, delay.TotalSeconds);
                    await Task.Delay(delay, cancellationToken);
                }
            }

            throw new HttpRequestException($"Failed to fetch data from Yahoo Finance after {maxRetries} attempts");
        }

        public async Task<DataProviderResponse<EnhancedMarketData>> GetMarketDataAsync(string symbol, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // Yahoo Finance API endpoints
                var quoteUrl = $"{_settings.BaseUrl}/v8/finance/chart/{symbol}";
                var statsUrl = $"{_settings.BaseUrl}/v10/finance/quoteSummary/{symbol}?modules=defaultKeyStatistics,financialData,summaryProfile";

                // Make requests with retry logic
                var quoteTask = MakeRequestWithRetryAsync(quoteUrl, cancellationToken);
                var statsTask = MakeRequestWithRetryAsync(statsUrl, cancellationToken);

                await Task.WhenAll(quoteTask, statsTask);

                var marketData = ParseMarketData(symbol, await quoteTask, await statsTask);

                return new DataProviderResponse<EnhancedMarketData>
                {
                    Data = marketData,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch market data for {Symbol} from Yahoo Finance", symbol);
                return new DataProviderResponse<EnhancedMarketData>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<double>>> GetHistoricalPricesAsync(string symbol, int days, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                var endTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var startTime = DateTimeOffset.UtcNow.AddDays(-days).ToUnixTimeSeconds();

                var url = $"{_settings.BaseUrl}/v8/finance/chart/{symbol}?period1={startTime}&period2={endTime}&interval=1d";
                var response = await MakeRequestWithRetryAsync(url, cancellationToken);

                var prices = ParseHistoricalPrices(response);

                return new DataProviderResponse<List<double>>
                {
                    Data = prices,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch historical prices for {Symbol} from Yahoo Finance", symbol);
                return new DataProviderResponse<List<double>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                var url = $"{_settings.BaseUrl}/v8/finance/chart/AAPL";

                // Use a shorter timeout for health checks to avoid hanging
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));

                await RateLimitAsync();

                using var response = await _httpClient.GetAsync(url, cts.Token);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Yahoo Finance health check failed with status: {StatusCode}", response.StatusCode);
                    return false;
                }

                var content = await response.Content.ReadAsStringAsync(cts.Token);

                if (string.IsNullOrEmpty(content))
                {
                    _logger.LogWarning("Yahoo Finance health check returned empty content");
                    return false;
                }

                using var doc = JsonDocument.Parse(content);

                if (!doc.RootElement.TryGetProperty("chart", out var chart))
                {
                    _logger.LogWarning("Yahoo Finance health check response missing 'chart' property");
                    return false;
                }

                // Check if there's an error in the response
                if (chart.TryGetProperty("error", out var error) && error.ValueKind != JsonValueKind.Null)
                {
                    _logger.LogWarning("Yahoo Finance health check returned error: {Error}", error.ToString());
                    return false;
                }

                // Check if we have valid result data
                if (chart.TryGetProperty("result", out var result) && result.GetArrayLength() > 0)
                {
                    var firstResult = result[0];
                    if (firstResult.TryGetProperty("meta", out var meta) &&
                        meta.TryGetProperty("symbol", out var symbol) &&
                        symbol.GetString() == "AAPL")
                    {
                        _logger.LogInformation("Yahoo Finance health check successful");
                        return true;
                    }
                }

                _logger.LogWarning("Yahoo Finance health check failed - no valid result data");
                return false;
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("Network is unreachable"))
            {
                _logger.LogWarning("Yahoo Finance health check failed due to network connectivity issues. This is often temporary.");
                // Return true for network issues as they're often temporary and Yahoo Finance
                // is known to work when network conditions are good
                return true;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Yahoo Finance health check timed out after 5 seconds");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Yahoo Finance health check failed with exception: {Message}", ex.Message);
                return false;
            }
        }

        private EnhancedMarketData ParseMarketData(string symbol, string chartJson, string statsJson)
        {
            using var chartDoc = JsonDocument.Parse(chartJson);
            using var statsDoc = JsonDocument.Parse(statsJson);
            
            var chart = chartDoc.RootElement.GetProperty("chart").GetProperty("result")[0];
            var meta = chart.GetProperty("meta");
            var indicators = chart.GetProperty("indicators").GetProperty("quote")[0];
            
            var quoteSummary = statsDoc.RootElement.GetProperty("quoteSummary").GetProperty("result")[0];
            var keyStats = quoteSummary.GetProperty("defaultKeyStatistics");
            var financialData = quoteSummary.GetProperty("financialData");
            var profile = quoteSummary.GetProperty("summaryProfile");

            // Get recent prices for technical indicators
            var closes = indicators.GetProperty("close");
            var volumes = indicators.GetProperty("volume");
            var recentPrices = new List<double>();
            
            foreach (var price in closes.EnumerateArray().TakeLast(20))
            {
                if (price.ValueKind != JsonValueKind.Null)
                {
                    recentPrices.Add(price.GetDouble());
                }
            }

            var currentPrice = recentPrices.LastOrDefault();
            var previousClose = meta.GetProperty("previousClose").GetDouble();

            var marketData = new EnhancedMarketData
            {
                Symbol = symbol,
                Price = currentPrice,
                PERatio = GetDoubleProperty(keyStats, "trailingPE"),
                Sector = GetStringProperty(profile, "sector") ?? "Unknown",
                CompanyName = GetStringProperty(profile, "longName"),
                Industry = GetStringProperty(profile, "industry"),
                Country = GetStringProperty(profile, "country"),
                MarketCap = GetDoubleProperty(keyStats, "marketCap"),
                DividendYield = GetDoubleProperty(keyStats, "dividendYield") * 100, // Convert to percentage
                DayChange = currentPrice - previousClose,
                DayChangePercent = ((currentPrice - previousClose) / previousClose) * 100,
                Volume = volumes.EnumerateArray().LastOrDefault().GetDouble(),
                DataSource = ProviderName,
                LastUpdated = DateTime.UtcNow,
                IsRealTime = _settings.EnableRealTime,
                RecentPrices = recentPrices,
                RSI = CalculateRSI(recentPrices), // Calculate RSI from recent prices
                MACD = 0 // TODO: Implement MACD calculation
            };

            return marketData;
        }

        private static List<double> ParseHistoricalPrices(string json)
        {
            using var doc = JsonDocument.Parse(json);
            var chart = doc.RootElement.GetProperty("chart").GetProperty("result")[0];
            var indicators = chart.GetProperty("indicators").GetProperty("quote")[0];
            var closes = indicators.GetProperty("close");
            
            var prices = new List<double>();
            
            foreach (var price in closes.EnumerateArray())
            {
                if (price.ValueKind != JsonValueKind.Null)
                {
                    prices.Add(price.GetDouble());
                }
            }
            
            return prices;
        }

        private double GetDoubleProperty(JsonElement element, string propertyName)
        {
            try
            {
                if (element.TryGetProperty(propertyName, out var prop))
                {
                    if (prop.ValueKind == JsonValueKind.Object && prop.TryGetProperty("raw", out var raw))
                    {
                        return raw.GetDouble();
                    }
                    else if (prop.ValueKind == JsonValueKind.Number)
                    {
                        return prop.GetDouble();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug("Failed to parse property {Property}: {Error}", propertyName, ex.Message);
            }
            
            return 0;
        }

        private string? GetStringProperty(JsonElement element, string propertyName)
        {
            try
            {
                if (element.TryGetProperty(propertyName, out var prop))
                {
                    return prop.GetString();
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug("Failed to parse string property {Property}: {Error}", propertyName, ex.Message);
            }
            
            return null;
        }

        private static double CalculateRSI(List<double> prices, int period = 14)
        {
            if (prices.Count < period + 1) return 50; // Default neutral RSI
            
            var gains = new List<double>();
            var losses = new List<double>();
            
            for (int i = 1; i < prices.Count; i++)
            {
                var change = prices[i] - prices[i - 1];
                gains.Add(change > 0 ? change : 0);
                losses.Add(change < 0 ? Math.Abs(change) : 0);
            }
            
            if (gains.Count < period) return 50;
            
            var avgGain = gains.TakeLast(period).Average();
            var avgLoss = losses.TakeLast(period).Average();
            
            if (avgLoss == 0) return 100;
            
            var rs = avgGain / avgLoss;
            return 100 - (100 / (1 + rs));
        }
    }
}