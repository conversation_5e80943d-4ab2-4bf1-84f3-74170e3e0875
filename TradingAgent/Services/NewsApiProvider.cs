// Services/NewsApiProvider.cs
using System.Text.Json;
using Config;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TradingAgent.Models;

namespace TradingAgent.Services
{
    public class NewsApiProvider : INewsProvider
    {
        private readonly HttpClient _httpClient;
        private readonly NewsApiSettings _settings;
        private readonly ILogger<NewsApiProvider> _logger;

        public string ProviderName => "NewsAPI";

        public NewsApiProvider(HttpClient httpClient, IOptions<DataProviderSettings> settings, ILogger<NewsApiProvider> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.NewsApi;
            _logger = logger;
            
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
            _httpClient.DefaultRequestHeaders.Add("X-API-Key", _settings.ApiKey);
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetNewsAsync(string symbol, int lookbackDays, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-lookbackDays).ToString("yyyy-MM-dd");
                var toDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
                
                // Search for company-specific news
                var url = $"{_settings.BaseUrl}/everything?" +
                         $"q={symbol} OR \"{GetCompanyName(symbol)}\"&" +
                         $"from={fromDate}&" +
                         $"to={toDate}&" +
                         $"sortBy=publishedAt&" +
                         $"language=en&" +
                         $"pageSize=50";

                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                var newsItems = ParseNews(response, symbol);

                return new DataProviderResponse<List<NewsItem>>
                {
                    Data = newsItems,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch news for {Symbol} from NewsAPI", symbol);
                return new DataProviderResponse<List<NewsItem>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetMarketNewsAsync(int lookbackDays, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-lookbackDays).ToString("yyyy-MM-dd");
                var toDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
                
                // Search for general market news
                var url = $"{_settings.BaseUrl}/everything?" +
                         $"q=(stock market OR trading OR Wall Street OR S&P 500 OR Dow Jones OR NASDAQ)&" +
                         $"from={fromDate}&" +
                         $"to={toDate}&" +
                         $"sortBy=publishedAt&" +
                         $"language=en&" +
                         $"domains=reuters.com,bloomberg.com,cnbc.com,marketwatch.com,yahoo.com&" +
                         $"pageSize=30";

                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                var newsItems = ParseNews(response, "MARKET");

                return new DataProviderResponse<List<NewsItem>>
                {
                    Data = newsItems,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch market news from NewsAPI");
                return new DataProviderResponse<List<NewsItem>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                var url = $"{_settings.BaseUrl}/top-headlines?country=us&category=business&pageSize=1";
                var response = await _httpClient.GetStringAsync(url);
                
                using var doc = JsonDocument.Parse(response);
                return doc.RootElement.GetProperty("status").GetString() == "ok";
            }
            catch
            {
                return false;
            }
        }

        private static List<NewsItem> ParseNews(string json, string symbol)
        {
            using var doc = JsonDocument.Parse(json);
            var articles = doc.RootElement.GetProperty("articles");
            
            var newsItems = new List<NewsItem>();
            
            foreach (var article in articles.EnumerateArray())
            {
                var title = article.GetProperty("title").GetString() ?? "";
                var description = article.GetProperty("description").GetString() ?? "";
                var content = article.GetProperty("content").GetString() ?? "";
                var publishedAt = DateTime.Parse(article.GetProperty("publishedAt").GetString() ?? DateTime.UtcNow.ToString());
                
                // Combine description and content for summary
                var summary = !string.IsNullOrEmpty(description) ? description : 
                             !string.IsNullOrEmpty(content) ? content.Substring(0, Math.Min(content.Length, 200)) : 
                             title;

                var newsItem = new NewsItem
                {
                    Title = title,
                    Summary = summary,
                    Url = article.GetProperty("url").GetString() ?? "",
                    PublishedAt = publishedAt,
                    Source = article.GetProperty("source").GetProperty("name").GetString() ?? "",
                    SentimentScore = AnalyzeSentiment(title, summary), // Simple sentiment analysis
                    Keywords = ExtractKeywords(title, summary, symbol)
                };
                
                newsItems.Add(newsItem);
            }
            
            return newsItems.OrderByDescending(n => n.PublishedAt).ToList();
        }

        private static double AnalyzeSentiment(string title, string summary)
        {
            // Simple keyword-based sentiment analysis
            var text = $"{title} {summary}".ToLower();
            
            var positiveWords = new[] { "gain", "surge", "rally", "bullish", "growth", "profit", "earnings beat", 
                                      "upgrade", "buy", "strong", "positive", "record", "high", "up" };
            var negativeWords = new[] { "fall", "drop", "crash", "bearish", "loss", "decline", "downgrade", 
                                      "sell", "weak", "negative", "low", "down", "concern", "warning" };
            
            var positiveCount = positiveWords.Count(word => text.Contains(word));
            var negativeCount = negativeWords.Count(word => text.Contains(word));
            
            if (positiveCount == 0 && negativeCount == 0) return 0; // Neutral
            
            var totalCount = positiveCount + negativeCount;
            return (double)(positiveCount - negativeCount) / totalCount;
        }

        private static List<string> ExtractKeywords(string title, string summary, string symbol)
        {
            var text = $"{title} {summary}".ToLower();
            var keywords = new List<string>();
            
            // Financial keywords
            var financialTerms = new[] { "earnings", "revenue", "profit", "loss", "merger", "acquisition", 
                                       "ipo", "dividend", "buyback", "partnership", "contract", "deal",
                                       "guidance", "forecast", "outlook", "upgrade", "downgrade" };
            
            foreach (var term in financialTerms)
            {
                if (text.Contains(term))
                {
                    keywords.Add(term);
                }
            }
            
            // Add symbol if mentioned
            if (text.Contains(symbol.ToLower()))
            {
                keywords.Add(symbol);
            }
            
            return keywords.Distinct().ToList();
        }

        private static string GetCompanyName(string symbol)
        {
            // Simple mapping for common symbols
            return symbol.ToUpper() switch
            {
                "AAPL" => "Apple",
                "MSFT" => "Microsoft",
                "GOOGL" => "Google",
                "AMZN" => "Amazon",
                "TSLA" => "Tesla",
                "META" => "Meta",
                "NVDA" => "NVIDIA",
                "NFLX" => "Netflix",
                "V" => "Visa",
                "JPM" => "JPMorgan",
                _ => symbol
            };
        }
    }
}