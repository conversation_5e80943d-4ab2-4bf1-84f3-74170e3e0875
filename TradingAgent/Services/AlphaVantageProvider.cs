// Services/AlphaVantageProvider.cs
using System.Text.Json;
using Config;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TradingAgent.Models;

namespace TradingAgent.Services
{
    public class AlphaVantageProvider : IMarketDataProvider, INewsProvider
    {
        private readonly HttpClient _httpClient;
        private readonly AlphaVantageSettings _settings;
        private readonly ILogger<AlphaVantageProvider> _logger;

        public string ProviderName => "AlphaVantage";

        public AlphaVantageProvider(HttpClient httpClient, IOptions<DataProviderSettings> settings, ILogger<AlphaVantageProvider> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.AlphaVantage;
            _logger = logger;
            
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
        }

        public async Task<DataProviderResponse<EnhancedMarketData>> GetMarketDataAsync(string symbol, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                // Get quote data
                var quoteUrl = $"{_settings.BaseUrl}?function=GLOBAL_QUOTE&symbol={symbol}&apikey={_settings.ApiKey}";
                var quoteResponse = await _httpClient.GetStringAsync(quoteUrl, cancellationToken);
                
                // Get company overview
                var overviewUrl = $"{_settings.BaseUrl}?function=OVERVIEW&symbol={symbol}&apikey={_settings.ApiKey}";
                var overviewResponse = await _httpClient.GetStringAsync(overviewUrl, cancellationToken);

                // Get technical indicators
                var rsiUrl = $"{_settings.BaseUrl}?function=RSI&symbol={symbol}&interval=daily&time_period=14&series_type=close&apikey={_settings.ApiKey}";
                var rsiResponse = await _httpClient.GetStringAsync(rsiUrl, cancellationToken);

                var marketData = ParseMarketData(symbol, quoteResponse, overviewResponse, rsiResponse);

                return new DataProviderResponse<EnhancedMarketData>
                {
                    Data = marketData,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch market data for {Symbol} from AlphaVantage", symbol);
                return new DataProviderResponse<EnhancedMarketData>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<double>>> GetHistoricalPricesAsync(string symbol, int days, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var url = $"{_settings.BaseUrl}?function=TIME_SERIES_DAILY&symbol={symbol}&outputsize=compact&apikey={_settings.ApiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var prices = ParseHistoricalPrices(response, days);

                return new DataProviderResponse<List<double>>
                {
                    Data = prices,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch historical prices for {Symbol} from AlphaVantage", symbol);
                return new DataProviderResponse<List<double>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetNewsAsync(string symbol, int lookbackDays, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                var url = $"{_settings.BaseUrl}?function=NEWS_SENTIMENT&tickers={symbol}&apikey={_settings.ApiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var newsItems = ParseNews(response, lookbackDays);

                return new DataProviderResponse<List<NewsItem>>
                {
                    Data = newsItems,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch news for {Symbol} from AlphaVantage", symbol);
                return new DataProviderResponse<List<NewsItem>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetMarketNewsAsync(int lookbackDays, CancellationToken cancellationToken = default)
        {
            // For market-wide news, we'll get general market sentiment
            return await GetNewsAsync("SPY", lookbackDays, cancellationToken);
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                var url = $"{_settings.BaseUrl}?function=GLOBAL_QUOTE&symbol=AAPL&apikey={_settings.ApiKey}";
                var response = await _httpClient.GetStringAsync(url);
                
                return !response.Contains("Error Message") && !response.Contains("Note:");
            }
            catch
            {
                return false;
            }
        }

        private EnhancedMarketData ParseMarketData(string symbol, string quoteJson, string overviewJson, string rsiJson)
        {
            using var quoteDoc = JsonDocument.Parse(quoteJson);
            using var overviewDoc = JsonDocument.Parse(overviewJson);
            
            var quote = quoteDoc.RootElement.GetProperty("Global Quote");
            var overview = overviewDoc.RootElement;

            var marketData = new EnhancedMarketData
            {
                Symbol = symbol,
                Price = ParseDouble(quote.GetProperty("05. price").GetString()),
                PERatio = ParseDouble(overview.GetProperty("PERatio").GetString()),
                Sector = overview.GetProperty("Sector").GetString() ?? "Unknown",
                CompanyName = overview.GetProperty("Name").GetString(),
                Industry = overview.GetProperty("Industry").GetString(),
                Country = overview.GetProperty("Country").GetString(),
                MarketCap = ParseDouble(overview.GetProperty("MarketCapitalization").GetString()),
                DividendYield = ParseDouble(overview.GetProperty("DividendYield").GetString()),
                DayChange = ParseDouble(quote.GetProperty("09. change").GetString()),
                DayChangePercent = ParsePercentage(quote.GetProperty("10. change percent").GetString()),
                Volume = ParseDouble(quote.GetProperty("06. volume").GetString()),
                DataSource = ProviderName,
                LastUpdated = DateTime.UtcNow,
                IsRealTime = false,
                RecentPrices = new List<double>() // Will be populated separately
            };

            // Parse RSI if available
            try
            {
                using var rsiDoc = JsonDocument.Parse(rsiJson);
                var rsiData = rsiDoc.RootElement.GetProperty("Technical Analysis: RSI");
                var latestDate = rsiData.EnumerateObject().FirstOrDefault();
                if (latestDate.Value.ValueKind != JsonValueKind.Undefined)
                {
                    marketData.RSI = ParseDouble(latestDate.Value.GetProperty("RSI").GetString());
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to parse RSI data: {Error}", ex.Message);
                marketData.RSI = 50; // Default neutral RSI
            }

            return marketData;
        }

        private static List<double> ParseHistoricalPrices(string json, int days)
        {
            using var doc = JsonDocument.Parse(json);
            var timeSeries = doc.RootElement.GetProperty("Time Series (Daily)");
            
            var prices = new List<double>();
            int count = 0;
            
            foreach (var day in timeSeries.EnumerateObject())
            {
                if (count >= days) break;
                
                var closePrice = ParseDouble(day.Value.GetProperty("4. close").GetString());
                prices.Add(closePrice);
                count++;
            }
            
            prices.Reverse(); // Return in chronological order
            return prices;
        }

        private static List<NewsItem> ParseNews(string json, int lookbackDays)
        {
            using var doc = JsonDocument.Parse(json);
            var feed = doc.RootElement.GetProperty("feed");
            
            var newsItems = new List<NewsItem>();
            var cutoffDate = DateTime.UtcNow.AddDays(-lookbackDays);
            
            foreach (var article in feed.EnumerateArray())
            {
                var publishedDate = DateTime.Parse(article.GetProperty("time_published").GetString() ?? "");
                if (publishedDate < cutoffDate) continue;
                
                var newsItem = new NewsItem
                {
                    Title = article.GetProperty("title").GetString() ?? "",
                    Summary = article.GetProperty("summary").GetString() ?? "",
                    Url = article.GetProperty("url").GetString() ?? "",
                    PublishedAt = publishedDate,
                    Source = article.GetProperty("source").GetString() ?? "",
                    SentimentScore = ParseDouble(article.GetProperty("overall_sentiment_score").GetString())
                };
                
                newsItems.Add(newsItem);
            }
            
            return newsItems.OrderByDescending(n => n.PublishedAt).ToList();
        }

        private static double ParseDouble(string? value)
        {
            if (string.IsNullOrEmpty(value) || value == "None") return 0;
            
            // Remove any non-numeric characters except decimal point and minus
            var cleanValue = new string(value.Where(c => char.IsDigit(c) || c == '.' || c == '-').ToArray());
            
            return double.TryParse(cleanValue, out var result) ? result : 0;
        }

        private static double ParsePercentage(string? value)
        {
            if (string.IsNullOrEmpty(value)) return 0;
            
            var cleanValue = value.Replace("%", "");
            return ParseDouble(cleanValue);
        }
    }
}