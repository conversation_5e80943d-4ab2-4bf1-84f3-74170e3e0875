// Services/IDataProvider.cs
using TradingAgent.Models;

namespace TradingAgent.Services
{
    /// <summary>
    /// Interface for market data providers
    /// </summary>
    public interface IMarketDataProvider
    {
        string ProviderName { get; }
        Task<DataProviderResponse<EnhancedMarketData>> GetMarketDataAsync(string symbol, CancellationToken cancellationToken = default);
        Task<DataProviderResponse<List<double>>> GetHistoricalPricesAsync(string symbol, int days, CancellationToken cancellationToken = default);
        Task<bool> IsHealthyAsync();
    }

    /// <summary>
    /// Interface for news data providers
    /// </summary>
    public interface INewsProvider
    {
        string ProviderName { get; }
        Task<DataProviderResponse<List<NewsItem>>> GetNewsAsync(string symbol, int lookbackDays, CancellationToken cancellationToken = default);
        Task<DataProviderResponse<List<NewsItem>>> GetMarketNewsAsync(int lookbackDays, CancellationToken cancellationToken = default);
        Task<bool> IsHealthyAsync();
    }

    /// <summary>
    /// Interface for economic data providers
    /// </summary>
    public interface IEconomicDataProvider
    {
        string ProviderName { get; }
        Task<DataProviderResponse<EconomicIndicators>> GetEconomicIndicatorsAsync(CancellationToken cancellationToken = default);
        Task<bool> IsHealthyAsync();
    }

    /// <summary>
    /// Aggregated data service that coordinates all providers
    /// </summary>
    public interface IDataAggregationService
    {
        Task<DataProviderResponse<EnhancedMarketData>> GetCompleteMarketDataAsync(MarketDataRequest request, CancellationToken cancellationToken = default);
        Task<List<string>> GetAvailableSymbolsAsync(string? sector = null, CancellationToken cancellationToken = default);
        Task<Dictionary<string, bool>> GetProviderHealthStatusAsync();
    }
}