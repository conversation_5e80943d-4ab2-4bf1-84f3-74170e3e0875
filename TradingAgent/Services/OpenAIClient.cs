// Services/OpenAIClient.cs
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Config;
using TradingAgent.Models;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;

namespace TradingAgent.Services
{
    public interface IOpenAIClient
    {
        Task<string> AskAsync(string userPrompt, string? model = null, string? systemPrompt = null);
        string GetModelForAgent(string agentName);
    }

    public class OpenAIClient : IOpenAIClient, IDisposable
    {
        private readonly HttpClient _http;
        private readonly OpenAISettings _settings;
        private readonly ILogger<OpenAIClient> _logger;

        public OpenAIClient(IOptions<OpenAISettings> settings, ILogger<OpenAIClient> logger)
        {
            _settings = settings.Value;
            _logger = logger;
            
            _http = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds)
            };
            
            var apiKey = _settings.ApiKey ?? Environment.GetEnvironmentVariable("OPENAI_API_KEY");
            if (string.IsNullOrEmpty(apiKey))
            {
                throw new InvalidOperationException("OpenAI API key is not configured. Set it in appsettings.json or OPENAI_API_KEY environment variable.");
            }
            
            _http.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
        }

        public async Task<string> AskAsync(string userPrompt, string? model = null, string? systemPrompt = null)
        {
            var selectedModel = model ?? _settings.DefaultModel;
            var systemMessage = systemPrompt ?? "You're a professional financial analyst.";
            
            // Validate model selection
            if (!GptModels.All.Contains(selectedModel))
            {
                _logger.LogWarning("Model {Model} not found in supported models. Using default: {DefaultModel}", 
                    selectedModel, _settings.DefaultModel);
                selectedModel = _settings.DefaultModel;
            }

            var body = new
            {
                model = selectedModel,
                messages = new[] {
                    new { role = "system", content = systemMessage },
                    new { role = "user", content = userPrompt }
                },
                temperature = 0.7,
                max_tokens = 1000
            };

            var requestJson = JsonSerializer.Serialize(body);
            _logger.LogDebug("Sending request to OpenAI with model: {Model}", selectedModel);

            for (int attempt = 1; attempt <= _settings.MaxRetries; attempt++)
            {
                try
                {
                    var response = await _http.PostAsync($"{_settings.BaseUrl}/chat/completions",
                        new StringContent(requestJson, Encoding.UTF8, "application/json"));

                    if (response.IsSuccessStatusCode)
                    {
                        var json = await response.Content.ReadAsStringAsync();
                        using var doc = JsonDocument.Parse(json);
                        var content = doc.RootElement.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString();
                        
                        _logger.LogDebug("Successfully received response from OpenAI");
                        return content ?? string.Empty;
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        _logger.LogError("OpenAI API error (attempt {Attempt}): {StatusCode} - {Content}", 
                            attempt, response.StatusCode, errorContent);
                        
                        if (attempt == _settings.MaxRetries)
                        {
                            throw new HttpRequestException($"OpenAI API request failed after {_settings.MaxRetries} attempts: {response.StatusCode}");
                        }
                        
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt))); // Exponential backoff
                    }
                }
                catch (Exception ex) when (attempt < _settings.MaxRetries)
                {
                    _logger.LogError(ex, "OpenAI API request failed (attempt {Attempt})", attempt);
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt)));
                }
            }

            throw new InvalidOperationException($"Failed to get response from OpenAI after {_settings.MaxRetries} attempts");
        }

        public string GetModelForAgent(string agentName)
        {
            return agentName switch
            {
                "FundamentalAnalyst" => _settings.AgentModels.FundamentalAnalyst,
                "SentimentAnalyst" => _settings.AgentModels.SentimentAnalyst,
                "NewsAnalyst" => _settings.AgentModels.NewsAnalyst,
                "RiskAnalyst" => _settings.AgentModels.RiskAnalyst,
                _ => _settings.DefaultModel
            };
        }

        public void Dispose() => _http?.Dispose();
    }
}