// Services/FinnhubProvider.cs
using System.Text.Json;
using Config;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TradingAgent.Models;

namespace TradingAgent.Services
{
    public class FinnhubProvider : IMarketDataProvider, INewsProvider
    {
        private readonly HttpClient _httpClient;
        private readonly FinnhubSettings _settings;
        private readonly ILogger<FinnhubProvider> _logger;

        public string ProviderName => "Finnhub";

        public FinnhubProvider(HttpClient httpClient, IOptions<DataProviderSettings> settings, ILogger<FinnhubProvider> logger)
        {
            _httpClient = httpClient;
            _settings = settings.Value.Finnhub;
            _logger = logger;
            
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
        }

        public async Task<DataProviderResponse<EnhancedMarketData>> GetMarketDataAsync(string symbol, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                if (string.IsNullOrEmpty(_settings.ApiKey))
                {
                    throw new InvalidOperationException("Finnhub API key is not configured");
                }

                // Get quote data
                var quoteUrl = $"{_settings.BaseUrl}/quote?symbol={symbol}&token={_settings.ApiKey}";
                var quoteTask = _httpClient.GetStringAsync(quoteUrl, cancellationToken);
                
                // Get company profile
                var profileUrl = $"{_settings.BaseUrl}/stock/profile2?symbol={symbol}&token={_settings.ApiKey}";
                var profileTask = _httpClient.GetStringAsync(profileUrl, cancellationToken);

                await Task.WhenAll(quoteTask, profileTask);

                var marketData = ParseMarketData(symbol, await quoteTask, await profileTask);

                return new DataProviderResponse<EnhancedMarketData>
                {
                    Data = marketData,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch market data for {Symbol} from Finnhub", symbol);
                return new DataProviderResponse<EnhancedMarketData>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<double>>> GetHistoricalPricesAsync(string symbol, int days, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                if (string.IsNullOrEmpty(_settings.ApiKey))
                {
                    throw new InvalidOperationException("Finnhub API key is not configured");
                }

                var endDate = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var startDate = DateTimeOffset.UtcNow.AddDays(-days).ToUnixTimeSeconds();
                
                var url = $"{_settings.BaseUrl}/stock/candle?symbol={symbol}&resolution=D&from={startDate}&to={endDate}&token={_settings.ApiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var prices = ParseHistoricalPrices(response);

                return new DataProviderResponse<List<double>>
                {
                    Data = prices,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch historical prices for {Symbol} from Finnhub", symbol);
                return new DataProviderResponse<List<double>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetNewsAsync(string symbol, int lookbackDays, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                if (string.IsNullOrEmpty(_settings.ApiKey))
                {
                    throw new InvalidOperationException("Finnhub API key is not configured");
                }

                var fromDate = DateTime.UtcNow.AddDays(-lookbackDays).ToString("yyyy-MM-dd");
                var toDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
                
                var url = $"{_settings.BaseUrl}/company-news?symbol={symbol}&from={fromDate}&to={toDate}&token={_settings.ApiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var newsItems = ParseNews(response, symbol);

                return new DataProviderResponse<List<NewsItem>>
                {
                    Data = newsItems,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch news for {Symbol} from Finnhub", symbol);
                return new DataProviderResponse<List<NewsItem>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<DataProviderResponse<List<NewsItem>>> GetMarketNewsAsync(int lookbackDays, CancellationToken cancellationToken = default)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                if (string.IsNullOrEmpty(_settings.ApiKey))
                {
                    throw new InvalidOperationException("Finnhub API key is not configured");
                }

                var url = $"{_settings.BaseUrl}/news?category=general&token={_settings.ApiKey}";
                var response = await _httpClient.GetStringAsync(url, cancellationToken);
                
                var newsItems = ParseGeneralNews(response, lookbackDays);

                return new DataProviderResponse<List<NewsItem>>
                {
                    Data = newsItems,
                    Success = true,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch market news from Finnhub");
                return new DataProviderResponse<List<NewsItem>>
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    DataSource = ProviderName,
                    ResponseTime = stopwatch.Elapsed
                };
            }
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_settings.ApiKey))
                {
                    return false;
                }

                var url = $"{_settings.BaseUrl}/quote?symbol=AAPL&token={_settings.ApiKey}";
                var response = await _httpClient.GetStringAsync(url);
                
                using var doc = JsonDocument.Parse(response);
                // Check if we got a valid response with current price
                return doc.RootElement.TryGetProperty("c", out var currentPrice) && currentPrice.GetDouble() > 0;
            }
            catch
            {
                return false;
            }
        }

        private EnhancedMarketData ParseMarketData(string symbol, string quoteJson, string profileJson)
        {
            using var quoteDoc = JsonDocument.Parse(quoteJson);
            using var profileDoc = JsonDocument.Parse(profileJson);
            
            var quote = quoteDoc.RootElement;
            var profile = profileDoc.RootElement;

            var currentPrice = quote.GetProperty("c").GetDouble();
            var previousClose = quote.GetProperty("pc").GetDouble();
            var dayChange = currentPrice - previousClose;
            var dayChangePercent = previousClose > 0 ? (dayChange / previousClose) * 100 : 0;

            var marketData = new EnhancedMarketData
            {
                Symbol = symbol,
                Price = currentPrice,
                DayChange = dayChange,
                DayChangePercent = dayChangePercent,
                Volume = quote.TryGetProperty("v", out var volume) ? volume.GetDouble() : null,
                DataSource = ProviderName,
                LastUpdated = DateTime.UtcNow,
                IsRealTime = true,
                RecentPrices = new List<double>() // Will be populated separately if needed
            };

            // Parse company profile data if available
            if (profile.ValueKind != JsonValueKind.Null)
            {
                marketData.CompanyName = GetStringProperty(profile, "name");
                marketData.Industry = GetStringProperty(profile, "finnhubIndustry");
                marketData.Country = GetStringProperty(profile, "country");
                marketData.Sector = GetStringProperty(profile, "finnhubIndustry") ?? "Unknown"; // Finnhub uses industry as sector
                marketData.MarketCap = GetDoubleProperty(profile, "marketCapitalization");
                
                // Calculate basic PE ratio if we have share outstanding and current price
                var sharesOutstanding = GetDoubleProperty(profile, "shareOutstanding");
                if (sharesOutstanding.HasValue && sharesOutstanding > 0)
                {
                    // This is a simplified PE calculation - in reality you'd need earnings data
                    // For now, we'll leave it as 0 and let other providers fill this data
                    marketData.PERatio = 0;
                }
            }

            // Set default RSI to neutral since Finnhub doesn't provide technical indicators in free tier
            marketData.RSI = 50;

            return marketData;
        }

        private List<double> ParseHistoricalPrices(string json)
        {
            var prices = new List<double>();

            try
            {
                using var doc = JsonDocument.Parse(json);
                var root = doc.RootElement;

                // Check if the response has data
                if (root.TryGetProperty("s", out var status) && status.GetString() == "ok")
                {
                    if (root.TryGetProperty("c", out var closePrices))
                    {
                        foreach (var price in closePrices.EnumerateArray())
                        {
                            prices.Add(price.GetDouble());
                        }
                    }
                }

                // Reverse to get chronological order (most recent last)
                prices.Reverse();
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to parse historical prices: {Error}", ex.Message);
            }

            return prices;
        }

        private List<NewsItem> ParseNews(string json, string symbol)
        {
            var newsItems = new List<NewsItem>();

            try
            {
                using var doc = JsonDocument.Parse(json);

                foreach (var item in doc.RootElement.EnumerateArray())
                {
                    var newsItem = new NewsItem
                    {
                        Title = GetStringProperty(item, "headline"),
                        Summary = GetStringProperty(item, "summary"),
                        Url = GetStringProperty(item, "url"),
                        Source = GetStringProperty(item, "source") ?? "Finnhub",
                        PublishedAt = DateTimeOffset.FromUnixTimeSeconds(item.GetProperty("datetime").GetInt64()).DateTime,
                        SentimentScore = GetDoubleProperty(item, "sentiment") ?? 0.0, // Finnhub provides sentiment in some cases
                        Keywords = new List<string> { symbol } // Add the symbol as a keyword
                    };

                    // Extract keywords from category if available
                    if (item.TryGetProperty("category", out var category))
                    {
                        var categoryStr = category.GetString();
                        if (!string.IsNullOrEmpty(categoryStr))
                        {
                            newsItem.Keywords.Add(categoryStr);
                        }
                    }

                    newsItems.Add(newsItem);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to parse news data: {Error}", ex.Message);
            }

            return newsItems;
        }

        private List<NewsItem> ParseGeneralNews(string json, int lookbackDays)
        {
            var newsItems = new List<NewsItem>();
            var cutoffDate = DateTime.UtcNow.AddDays(-lookbackDays);

            try
            {
                using var doc = JsonDocument.Parse(json);

                foreach (var item in doc.RootElement.EnumerateArray())
                {
                    var publishedAt = DateTimeOffset.FromUnixTimeSeconds(item.GetProperty("datetime").GetInt64()).DateTime;

                    // Filter by lookback days
                    if (publishedAt < cutoffDate)
                        continue;

                    var newsItem = new NewsItem
                    {
                        Title = GetStringProperty(item, "headline"),
                        Summary = GetStringProperty(item, "summary"),
                        Url = GetStringProperty(item, "url"),
                        Source = GetStringProperty(item, "source") ?? "Finnhub",
                        PublishedAt = publishedAt,
                        SentimentScore = GetDoubleProperty(item, "sentiment") ?? 0.0,
                        Keywords = new List<string> { "market", "general" }
                    };

                    // Extract keywords from category if available
                    if (item.TryGetProperty("category", out var category))
                    {
                        var categoryStr = category.GetString();
                        if (!string.IsNullOrEmpty(categoryStr))
                        {
                            newsItem.Keywords.Add(categoryStr);
                        }
                    }

                    newsItems.Add(newsItem);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to parse general news data: {Error}", ex.Message);
            }

            return newsItems;
        }

        private static string? GetStringProperty(JsonElement element, string propertyName)
        {
            return element.TryGetProperty(propertyName, out var property) && property.ValueKind == JsonValueKind.String
                ? property.GetString()
                : null;
        }

        private static double? GetDoubleProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var property))
            {
                return property.ValueKind switch
                {
                    JsonValueKind.Number => property.GetDouble(),
                    JsonValueKind.String when double.TryParse(property.GetString(), out var result) => result,
                    _ => null
                };
            }
            return null;
        }
    }
}
