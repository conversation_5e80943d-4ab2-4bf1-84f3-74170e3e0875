# .dockerignore for TradingAgent

# Build outputs
bin/
obj/
out/
publish/

# Environment files (security)
.env
.env.*
!.env.example

# IDE files
.vs/
.vscode/
*.user
*.suo
*.cache

# Logs
logs/
*.log

# Documentation
*.md
!README.md

# Git
.git/
.gitignore

# Docker files
Dockerfile*
docker-compose*

# Test files
**/TestResults/
**/*.trx

# Temporary files
**/tmp/
**/temp/

# OS files
.DS_Store
Thumbs.db

# Node modules (if any)
node_modules/

# Package files
*.nupkg
*.snupkg

# User secrets
secrets.json

# Local development files
launchSettings.json
