# TradingAgent - Enhanced AI-Powered Trading Analysis System

## Overview

TradingAgent is a comprehensive AI-powered trading analysis platform that automatically fetches market data, news, and economic indicators to provide intelligent trading recommendations through a multi-agent system.

## Key Features

### 🚀 **Autonomous Data Fetching**
- **Yahoo Finance Integration**: Real-time market data, quotes, and historical prices
- **Alpha Vantage**: Professional financial data and news sentiment
- **NewsAPI**: Recent news analysis and sentiment scoring
- **FRED Economic Data**: Federal Reserve economic indicators
- **Smart Caching**: Configurable cache for optimal performance
- **Fallback System**: Automatic failover between data providers

### 🤖 **AI-Powered Agent System**
- **FundamentalAnalyst**: Company financials and valuation analysis
- **SentimentAnalyst**: News sentiment and market mood analysis  
- **NewsAnalyst**: Impact analysis of recent news events
- **RiskAnalyst**: Comprehensive risk assessment and position sizing
- **MomentumAnalyst**: Technical momentum indicators (RSI, MACD)
- **SupportResistanceAnalyst**: Price level and technical analysis

### ⚙️ **Dynamic Model Selection**
- **Configurable AI Models**: GPT-4o, GPT-4, GPT-3.5-turbo support
- **Agent-Specific Models**: Different models for different analysis types
- **Cost Optimization**: Smart model selection based on task complexity
- **Runtime Model Switching**: Change models without restarting

## Quick Start

### 1. Configuration

Set up your API keys in `appsettings.json`:

```json
{
  "OpenAI": {
    "ApiKey": "your-openai-api-key"
  },
  "DataProviders": {
    "AlphaVantage": {
      "ApiKey": "your-alphavantage-key"
    },
    "NewsApi": {
      "ApiKey": "your-newsapi-key"
    },
    "Fred": {
      "ApiKey": "your-fred-key"
    }
  }
}
```

### 2. Basic Usage

#### Automatic Analysis (Recommended)
```http
POST /analyze/symbol/AAPL
```

#### Custom Parameters
```http
POST /analyze/symbol/TSLA?includeNews=true&includeEconomicData=true&newsLookbackDays=3
```

#### Manual Data Input (Legacy)
```http
POST /analyze
Content-Type: application/json

{
  "symbol": "AAPL",
  "price": 150.0,
  "peRatio": 25.5,
  "sector": "Technology"
}
```

## API Endpoints

### Analysis Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/analyze/symbol/{symbol}` | POST | Auto-fetch data and analyze symbol |
| `/analyze/symbols` | GET | Get available symbols |
| `/analyze/symbols?sector=technology` | GET | Get symbols by sector |
| `/analyze/health` | GET | Check data provider health |
| `/analyze` | POST | Analyze with manual data |

### Model Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/models/available` | GET | List all supported AI models |
| `/models/configuration` | GET | Current model configuration |
| `/models/test` | POST | Test specific model connectivity |
| `/models/agent/{agent}` | PUT | Update agent model assignment |

## Data Providers

### Market Data Providers
- **Yahoo Finance** (Primary): Free, real-time quotes and historical data
- **Alpha Vantage**: Professional-grade financial data with sentiment
- **Finnhub**: Real-time financial data with company profiles and news
- **Polygon.io**: Real-time and historical market data

### News Providers  
- **NewsAPI**: Global news aggregation with filtering
- **Alpha Vantage News**: Financial news with sentiment scoring

### Economic Data
- **FRED (Federal Reserve)**: Economic indicators and government data

## Response Format

```json
{
  "analysis": {
    "bias": "BUY",
    "confidence": 0.847,
    "processingTimeMs": 1247.3,
    "timestamp": "2024-01-15T10:30:00Z",
    "details": [
      {
        "agent": "FundamentalAnalyst",
        "insight": "Strong earnings growth and reasonable valuation suggest BUY",
        "confidence": 0.85,
        "model": "gpt-4o"
      },
      {
        "agent": "SentimentAnalyst", 
        "insight": "Positive news sentiment with average score of 0.42",
        "confidence": 0.78,
        "model": "gpt-3.5-turbo"
      }
    ]
  },
  "dataMetadata": {
    "dataSource": "YahooFinance",
    "fromCache": false,
    "dataFetchTime": 892.1,
    "newsCount": 12,
    "hasEconomicData": true
  }
}
```

## Configuration Options

### Data Provider Settings
```json
{
  "DataProviders": {
    "PrimaryMarketDataProvider": "YahooFinance",
    "PrimaryNewsProvider": "NewsAPI", 
    "MarketDataCacheMinutes": 5,
    "NewsCacheMinutes": 15,
    "EnableFallback": true
  }
}
```

### AI Model Configuration
```json
{
  "OpenAI": {
    "DefaultModel": "gpt-4o",
    "AgentModels": {
      "FundamentalAnalyst": "gpt-4o",
      "SentimentAnalyst": "gpt-3.5-turbo",
      "NewsAnalyst": "gpt-4o", 
      "RiskAnalyst": "gpt-4"
    }
  }
}
```

## Environment Variables

Alternatively, set API keys via environment variables:
- `OPENAI_API_KEY`
- `ALPHAVANTAGE_API_KEY`
- `NEWSAPI_API_KEY`
- `FRED_API_KEY`

## Performance Features

### Caching Strategy
- **Market Data**: 5-minute cache (configurable)
- **News Data**: 15-minute cache (configurable)
- **Economic Data**: 1-hour cache (less volatile)

### Rate Limiting
- Automatic rate limiting per provider
- Smart request queuing
- Exponential backoff on failures

### Fallback System
- Primary provider failure → automatic fallback
- Multiple provider support for redundancy
- Health monitoring for all providers

## Supported Models

| Model | Cost Tier | Best For |
|-------|-----------|----------|
| GPT-4o | Medium | Balanced performance and cost |
| GPT-4 | High | Complex analysis requiring reasoning |
| GPT-3.5-turbo | Low | Fast analysis and cost optimization |
| GPT-4o-mini | Low | Quick analysis tasks |

## Error Handling

The system includes comprehensive error handling:
- **Provider failures**: Automatic fallback to alternative providers
- **API rate limits**: Built-in retry logic with exponential backoff
- **Invalid symbols**: Clear error messages
- **Network issues**: Timeout handling and retries

## Development

### Building
```bash
dotnet build
```

### Running
```bash
dotnet run
```

### Testing
```bash
# Test the API
curl http://localhost:5285/analyze/symbol/AAPL

# Check provider health
curl http://localhost:5285/analyze/health
```

## Roadmap

- [ ] **Real-time WebSocket feeds** for live price updates
- [ ] **Options flow analysis** integration
- [ ] **Cryptocurrency support** via additional providers
- [ ] **Portfolio-level analysis** across multiple positions
- [ ] **Custom agent creation** via configuration
- [ ] **Advanced technical indicators** (Bollinger Bands, Stochastic)
- [ ] **Backtesting framework** for strategy validation

## License

This project is licensed under the MIT License.