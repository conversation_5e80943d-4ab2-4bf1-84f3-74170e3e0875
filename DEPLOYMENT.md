# 🚀 TradingAgent Deployment Guide

This guide explains how to set up and troubleshoot the CI/CD pipeline for TradingAgent.

## 📋 Prerequisites

### 1. GitHub Repository Setup
- Repository must be public or have GitHub Packages enabled
- GitHub Actions must be enabled

### 2. Server Requirements
- Docker and Docker Compose installed
- SSH access configured
- Ports 5285, 6379, 5432 available

### 3. Required GitHub Secrets

Set these secrets in your GitHub repository (`Settings > Secrets and variables > Actions`):

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `HOST` | Server IP or hostname | `*************` |
| `USERNAME` | SSH username | `ubuntu` |
| `SSH_KEY` | Private SSH key | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `DEPLOY_PATH` | Deployment directory | `/opt/trading-agent` |

## 🔧 Server Setup

### 1. Prepare Deployment Directory

```bash
# Create deployment directory
sudo mkdir -p /opt/trading-agent
sudo chown $USER:$USER /opt/trading-agent
cd /opt/trading-agent

# Clone repository (or copy files)
git clone https://github.com/yourusername/AgentServer.git .

# Copy environment file
cp TradingAgent/.env.example .env
# Edit .env with your API keys
nano .env
```

### 2. Test Local Deployment

```bash
# Test docker-compose setup
docker-compose config

# Run deployment script
./deploy.sh
```

## 🔍 Troubleshooting CI/CD Issues

### Issue 1: "Workflow file not found"
**Problem**: GitHub Actions can't find the workflow file.

**Solution**: Ensure the file is at `.github/workflows/ci-cd.yml` (not in `config/` directory).

### Issue 2: "Docker build failed"
**Problem**: Build context or Dockerfile path is incorrect.

**Solutions**:
- Check that `TradingAgent/Dockerfile` exists
- Verify build context in workflow points to `./TradingAgent`

### Issue 3: "Permission denied" during deployment
**Problem**: SSH key or server permissions are incorrect.

**Solutions**:
- Verify SSH key is correct and has no passphrase
- Check server user has Docker permissions: `sudo usermod -aG docker $USER`
- Ensure deployment directory is writable

### Issue 4: "Image not found" during deployment
**Problem**: Docker image wasn't pushed to registry or pull failed.

**Solutions**:
- Check GitHub Packages permissions
- Verify `GITHUB_TOKEN` has package write permissions
- Ensure repository is public or packages are accessible

### Issue 5: "Health check failed"
**Problem**: Service starts but health endpoint is not responding.

**Solutions**:
- Check service logs: `docker-compose logs trading-agent`
- Verify .env file has correct API keys
- Check port mapping and firewall settings

## 📊 Monitoring Deployment

### Check Service Status
```bash
# View running containers
docker-compose ps

# Check service logs
docker-compose logs -f trading-agent

# Test health endpoint
curl http://localhost:5285/analyze/health
```

### View GitHub Actions Logs
1. Go to your repository on GitHub
2. Click "Actions" tab
3. Click on the latest workflow run
4. Expand each step to see detailed logs

## 🔄 Manual Deployment

If CI/CD is not working, you can deploy manually:

```bash
# On your server
cd /opt/trading-agent

# Pull latest code
git pull origin main

# Update .env if needed
nano .env

# Deploy
./deploy.sh
```

## 🛠️ Advanced Configuration

### Custom Docker Registry
To use a different registry (e.g., Docker Hub):

1. Update `.github/workflows/ci-cd.yml`:
   ```yaml
   env:
     REGISTRY: docker.io
     IMAGE_NAME: yourusername/trading-agent
   ```

2. Add registry credentials to GitHub secrets:
   - `DOCKER_USERNAME`
   - `DOCKER_PASSWORD`

### Multiple Environments
To deploy to different environments (staging, production):

1. Create environment-specific secrets
2. Use different workflow files or conditions
3. Modify deployment paths accordingly

## 📞 Getting Help

If you're still having issues:

1. Check GitHub Actions logs for specific error messages
2. Verify all prerequisites are met
3. Test each component individually (build, push, deploy)
4. Check server logs and Docker status

Common log locations:
- GitHub Actions: Repository > Actions tab
- Server logs: `docker-compose logs`
- Application logs: `./logs/` directory
