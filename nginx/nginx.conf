events {
    worker_connections 1024;
}

http {
    upstream trading_agent {
        server trading-agent:8080;
    }

    server {
        listen 80;
        server_name localhost;

        # Redirect HTTP to HTTPS (optional)
        # return 301 https://$server_name$request_uri;

        # Or serve directly over HTTP
        location / {
            proxy_pass http://trading_agent;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://trading_agent/analyze/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    # HTTPS server (optional - requires SSL certificates)
    # server {
    #     listen 443 ssl;
    #     server_name localhost;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     location / {
    #         proxy_pass http://trading_agent;
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
    #     }
    # }
}
