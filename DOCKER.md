# 🐳 Docker Setup for TradingAgent

This guide covers running TradingAgent using Docker and Docker Compose with different configurations.

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Your API keys configured in `.env` file

## 🚀 Quick Start

### 1. Simple Setup (Recommended for Testing)

```bash
# Copy your API keys to .env file
cp TradingAgent/.env.example TradingAgent/.env
# Edit .env with your actual API keys

# Run with simple configuration
docker-compose -f docker-compose.simple.yml up --build
```

**Access:** http://localhost:5285

### 2. Full Production Setup

```bash
# Run with Redis, PostgreSQL, and monitoring
docker-compose up --build
```

**Services:**
- **TradingAgent API:** http://localhost:5285
- **Redis:** localhost:6379
- **PostgreSQL:** localhost:5432

### 3. Development Setup

```bash
# Run with development tools and hot reload
docker-compose -f docker-compose.dev.yml up --build
```

**Services:**
- **TradingAgent API:** http://localhost:5285
- **Adminer (DB Admin):** http://localhost:8080
- **Redis Commander:** http://localhost:8081

## 🔧 Configuration Options

### Environment Variables

Create a `.env` file in the project root:

```bash
# Required API Keys
ALPHAVANTAGE_API_KEY=your_key_here
IEXCLOUD_API_KEY=your_key_here
POLYGON_API_KEY=your_key_here
NEWSAPI_API_KEY=your_key_here
FRED_API_KEY=your_key_here
OPENAI_API_KEY=your_key_here

# Optional Configuration
PRIMARY_MARKET_DATA_PROVIDER=YahooFinance
PRIMARY_NEWS_PROVIDER=NewsAPI
MARKET_DATA_CACHE_MINUTES=5
NEWS_CACHE_MINUTES=15
IEX_USE_SANDBOX=false

# Database (for full setup)
POSTGRES_PASSWORD=your_secure_password
GRAFANA_PASSWORD=your_grafana_password
```

### Docker Compose Files

| File | Purpose | Services |
|------|---------|----------|
| `docker-compose.simple.yml` | Quick testing | TradingAgent only |
| `docker-compose.yml` | Production | TradingAgent + Redis + PostgreSQL + Nginx |
| `docker-compose.dev.yml` | Development | TradingAgent + Redis + PostgreSQL + Admin tools |

## 🧪 Testing Your Setup

### 1. Health Check
```bash
curl http://localhost:5285/analyze/health
```

### 2. Test Market Data
```bash
curl http://localhost:5285/analyze/symbol/AAPL
```

### 3. Check Available Symbols
```bash
curl http://localhost:5285/analyze/symbols
```

### 4. Test AI Models
```bash
curl http://localhost:5285/models/available
```

## 📊 Monitoring (Production Setup)

### Prometheus Metrics
- **URL:** http://localhost:9090
- **Metrics:** Application performance, API response times, provider health

### Grafana Dashboards
- **URL:** http://localhost:3000
- **Login:** admin / (your GRAFANA_PASSWORD)
- **Dashboards:** Pre-configured trading analysis dashboards

### Enable Monitoring
```bash
# Run with monitoring profile
docker-compose --profile monitoring up --build
```

## 🔍 Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker-compose logs trading-agent

# Check environment variables
docker-compose config
```

#### API Keys Not Working
```bash
# Verify .env file is loaded
docker exec trading-agent-simple env | grep API_KEY

# Test specific provider
curl "http://localhost:5285/analyze/symbol/AAPL?preferredDataSource=YahooFinance"
```

#### Port Conflicts
```bash
# Check what's using the port
sudo netstat -tulpn | grep :5285

# Use different ports
docker-compose -f docker-compose.simple.yml up --build -p 5286:8080
```

### Debug Mode

Enable detailed logging:
```bash
# Add to .env file
ENABLE_PROVIDER_DEBUG_LOGGING=true

# Restart containers
docker-compose restart
```

## 🚀 Production Deployment

### 1. Security Checklist
- [ ] Change default passwords
- [ ] Use secrets management
- [ ] Enable HTTPS with SSL certificates
- [ ] Configure firewall rules
- [ ] Set up log rotation

### 2. Performance Optimization
```bash
# Use production profile
docker-compose --profile production up -d

# Scale API instances
docker-compose up --scale trading-agent=3
```

### 3. Backup Strategy
```bash
# Backup PostgreSQL data
docker exec trading-agent-postgres pg_dump -U tradinguser tradingagent > backup.sql

# Backup Redis data
docker exec trading-agent-redis redis-cli BGSAVE
```

## 📝 Development Workflow

### 1. Code Changes
```bash
# Development with hot reload
docker-compose -f docker-compose.dev.yml up

# Rebuild after dependency changes
docker-compose -f docker-compose.dev.yml up --build
```

### 2. Database Management
- **Adminer:** http://localhost:8080
- **Server:** postgres-dev
- **Database:** tradingagent_dev
- **Username:** devuser
- **Password:** devpass123

### 3. Redis Management
- **Redis Commander:** http://localhost:8081
- **Connection:** local:redis-dev:6379

## 🔧 Custom Configuration

### Override Settings
Create `docker-compose.override.yml`:
```yaml
version: '3.8'
services:
  trading-agent:
    environment:
      - CUSTOM_SETTING=value
    ports:
      - "8080:8080"  # Different port
```

### Custom Dockerfile
```dockerfile
# Extend base image
FROM trading-agent:latest
COPY custom-config.json /app/
```

## 📚 Useful Commands

```bash
# View logs
docker-compose logs -f trading-agent

# Execute commands in container
docker exec -it trading-agent-simple bash

# Clean up
docker-compose down -v  # Remove volumes
docker system prune     # Clean unused images

# Update images
docker-compose pull
docker-compose up --build
```

## 🆘 Support

- **Logs:** Check `./logs/` directory
- **Health:** Monitor `/analyze/health` endpoint
- **Metrics:** Use Prometheus/Grafana for insights
- **Issues:** Report bugs with container logs

---

**Ready to trade?** Start with `docker-compose -f docker-compose.simple.yml up --build` 🚀
