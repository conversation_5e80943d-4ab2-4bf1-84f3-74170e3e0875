services:
  # TradingAgent API Service
  trading-agent:
    image: ghcr.io/arnyfesto1/agentserver/trading-agent:latest
    container_name: trading-agent-api
    ports:
      - "5285:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      # API Keys from environment variables
      - ALPHAVANTAGE_API_KEY=${ALPHAVANTAGE_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - POLYGON_API_KEY_SECONDARY=${POLYGON_API_KEY_SECONDARY}
      - NEWSAPI_API_KEY=${NEWSAPI_API_KEY}
      - FRED_API_KEY=${FRED_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      # Optional overrides
      - PRIMARY_MARKET_DATA_PROVIDER=${PRIMARY_MARKET_DATA_PROVIDER:-Finnhub}
      - PRIMARY_NEWS_PROVIDER=${PRIMARY_NEWS_PROVIDER:-NewsAPI}
      - MARKET_DATA_CACHE_MINUTES=${MARKET_DATA_CACHE_MINUTES:-5}
      - NEWS_CACHE_MINUTES=${NEWS_CACHE_MINUTES:-15}
    volumes:
      - ./logs:/app/logs
    networks:
      - trading-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/analyze/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - postgres

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: trading-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for data persistence
  postgres:
    image: postgres:15-alpine
    container_name: trading-agent-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=tradingagent
      - POSTGRES_USER=tradinguser
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - trading-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tradinguser -d tradingagent"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: trading-agent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - trading-network
    restart: unless-stopped
    depends_on:
      - trading-agent

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: trading-agent-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - trading-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: trading-agent-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - trading-network
    restart: unless-stopped
    depends_on:
      - prometheus
    profiles:
      - monitoring

networks:
  trading-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
