#!/bin/bash

# TradingAgent Deployment Script
# This script handles deployment using Docker Compose

set -e  # Exit on any error

echo "🚀 Starting TradingAgent Deployment"
echo "=================================="

# Configuration
COMPOSE_FILE="docker-compose.yml"
SERVICE_NAME="trading-agent"
IMAGE_NAME="ghcr.io/$GITHUB_REPOSITORY/trading-agent:latest"

# Function to check if service is healthy
check_health() {
    local service=$1
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Checking health of $service..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T $service curl -f http://localhost:8080/analyze/health >/dev/null 2>&1; then
            echo "✅ $service is healthy"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting for $service to be ready..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service failed health check after $max_attempts attempts"
    return 1
}

# Function to deploy service
deploy_service() {
    echo "📦 Pulling latest images..."
    docker-compose pull $SERVICE_NAME
    
    echo "🔄 Recreating $SERVICE_NAME service..."
    docker-compose up -d --force-recreate $SERVICE_NAME
    
    echo "⏳ Waiting for service to start..."
    sleep 30
    
    if check_health $SERVICE_NAME; then
        echo "✅ Deployment successful!"
        
        # Show service status
        echo ""
        echo "📊 Service Status:"
        docker-compose ps $SERVICE_NAME
        
        echo ""
        echo "📋 Service Logs (last 20 lines):"
        docker-compose logs --tail=20 $SERVICE_NAME
        
        return 0
    else
        echo "❌ Deployment failed - service is not healthy"
        
        echo ""
        echo "📋 Service Logs (last 50 lines):"
        docker-compose logs --tail=50 $SERVICE_NAME
        
        return 1
    fi
}

# Function to rollback on failure
rollback() {
    echo "🔄 Rolling back to previous version..."
    
    # This would require keeping track of previous image tags
    # For now, just restart the service
    docker-compose restart $SERVICE_NAME
    
    if check_health $SERVICE_NAME; then
        echo "✅ Rollback successful"
    else
        echo "❌ Rollback failed - manual intervention required"
    fi
}

# Main deployment logic
main() {
    # Check if docker-compose.yml exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        echo "❌ $COMPOSE_FILE not found in current directory"
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        echo "❌ .env file not found - please create it with your API keys"
        exit 1
    fi
    
    # Deploy the service
    if deploy_service; then
        echo ""
        echo "🎉 Deployment completed successfully!"
        echo "🌐 Service is available at: http://localhost:5285"
        echo "📊 Health check: http://localhost:5285/analyze/health"
    else
        echo ""
        echo "❌ Deployment failed"
        
        # Ask if user wants to rollback
        read -p "Do you want to attempt rollback? (y/N): " rollback_choice
        if [[ $rollback_choice =~ ^[Yy]$ ]]; then
            rollback
        fi
        
        exit 1
    fi
}

# Run main function
main "$@"
