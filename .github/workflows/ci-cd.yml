name: TradingAgent CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/trading-agent

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '10.0.x'

      - name: Restore dependencies
        run: dotnet restore TradingAgent/TradingAgent.csproj

      - name: Build
        run: dotnet build TradingAgent/TradingAgent.csproj --configuration Release --no-restore

      - name: Test
        run: dotnet test TradingAgent/TradingAgent.csproj --configuration Release --no-build --verbosity normal

  build-and-push-image:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to GitHub Container Registry
        run: echo "${{ secrets.GHCR_PAT }}" | docker login ghcr.io -u arnyfesto1 --password-stdin

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./TradingAgent
          file: ./TradingAgent/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push-image
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            # Navigate to deployment directory
            cd "/opt/trading-agent"

            # Login to GitHub Container Registry
            echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Update docker-compose.yml to use the new image
            sed -i "s|image:.*trading-agent.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest|g" docker-compose.yml || true

            # Pull latest images
            docker-compose pull trading-agent

            # Deploy using the deployment script
            if [ -f "deploy.sh" ]; then
              ./deploy.sh
            else
              # Fallback deployment
              docker-compose up -d --force-recreate trading-agent
            fi

            # Verify deployment
            sleep 30
            docker-compose ps trading-agent

            # Test health endpoint
            curl -f http://localhost:5285/analyze/health || echo "Health check failed"
