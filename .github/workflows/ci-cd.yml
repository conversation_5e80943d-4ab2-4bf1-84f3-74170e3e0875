name: TradingAgent CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/trading-agent

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '10.0.x'

      - name: Restore dependencies
        run: dotnet restore TradingAgent/TradingAgent.csproj

      - name: Build
        run: dotnet build TradingAgent/TradingAgent.csproj --configuration Release --no-restore

      - name: Test
        run: dotnet test TradingAgent/TradingAgent.csproj --configuration Release --no-build --verbosity normal

  build-and-push-image:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to GitHub Container Registry
        run: echo "${{ secrets.GHCR_PAT }}" | docker login ghcr.io -u arnyfesto1 --password-stdin

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./TradingAgent
          file: ./TradingAgent/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push-image
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            # Check if repository exists at root level
            REPO_DIR="$HOME/AgentServer"

            if [ ! -d "$REPO_DIR" ]; then
              echo "Repository not found at $REPO_DIR, cloning..."
              cd "$HOME"
              git clone https://github.com/arnyfesto1/AgentServer.git
              echo "Repository cloned to $REPO_DIR"
            else
              echo "Repository exists at $REPO_DIR, updating..."
              cd "$REPO_DIR"
              git fetch origin main
              git reset --hard origin/main
              echo "Repository updated from GitHub"
            fi

            # Ensure deployment directory exists
            sudo mkdir -p "$DEPLOY_DIR"
            sudo chown -R $USER:$USER "$DEPLOY_DIR"

            # Copy necessary files from repo to deployment directory
            echo "Copying files from repository to deployment directory..."
            cp "$REPO_DIR/docker-compose.yml" "$DEPLOY_DIR/"
            cp "$REPO_DIR/deploy.sh" "$DEPLOY_DIR/"
            chmod +x "$DEPLOY_DIR/deploy.sh"

            # Navigate to deployment directory
            cd "$DEPLOY_DIR"

            # Create .env file with secrets
            echo "Creating .env file with API keys..."
            cat > .env << 'EOF'
            # Trading Agent Environment Variables
            # Generated by GitHub Actions on $(date)

            # OpenAI API Key for AI-powered analysis agents
            # Get your API key at: https://platform.openai.com/api-keys
            OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}

            # Alpha Vantage API Key for market data
            # Get your API key at: https://www.alphavantage.co/support/#api-key
            ALPHAVANTAGE_API_KEY=${{ secrets.ALPHAVANTAGE_API_KEY }}

            # Finnhub API Key for real-time market data (replaces IEX Cloud)
            # Get your API key at: https://finnhub.io/register
            FINNHUB_API_KEY=${{ secrets.FINNHUB_API_KEY }}

            # Polygon.io API Keys for market data and news
            # Get your API key at: https://polygon.io/
            POLYGON_API_KEY=${{ secrets.POLYGON_API_KEY }}
            POLYGON_API_KEY_SECONDARY=${{ secrets.POLYGON_API_KEY_SECONDARY }}

            # NewsAPI Key for news data
            # Get your API key at: https://newsapi.org/register
            NEWSAPI_API_KEY=${{ secrets.NEWSAPI_API_KEY }}

            # FRED API Key for economic data
            # Get your API key at: https://fred.stlouisfed.org/docs/api/api_key.html
            FRED_API_KEY=${{ secrets.FRED_API_KEY }}

            # Database Configuration
            POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}

            # Optional: Grafana Admin Password
            GRAFANA_PASSWORD=${{ secrets.GRAFANA_PASSWORD }}

            # Data Provider Configuration
            PRIMARY_MARKET_DATA_PROVIDER=Finnhub
            PRIMARY_NEWS_PROVIDER=NewsAPI

            # Cache Configuration
            MARKET_DATA_CACHE_MINUTES=5
            NEWS_CACHE_MINUTES=15
            EOF

            # Set proper permissions for .env file
            chmod 600 .env
            echo ".env file created successfully"

            # Show current deployment files
            echo "Repository location: $REPO_DIR"
            echo "Deployment location: $DEPLOY_DIR"
            echo "Current deployment files:"
            ls -la
            echo "Docker-compose version info:"
            head -5 docker-compose.yml || echo "docker-compose.yml not found"

            # Login to GitHub Container Registry
            echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Update docker-compose.yml to use the new image
            sed -i "s|image:.*trading-agent.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest|g" docker-compose.yml || true

            # Pull latest images
            docker-compose pull trading-agent

            # Deploy using the deployment script
            if [ -f "deploy.sh" ]; then
              ./deploy.sh
            else
              # Fallback deployment
              docker-compose up -d --force-recreate trading-agent
            fi

            # Verify deployment
            sleep 30
            docker-compose ps trading-agent

            # Test health endpoint
            curl -f http://localhost:5285/analyze/health || echo "Health check failed"

            # Clean up sensitive information from logs
            echo "Deployment completed successfully"
